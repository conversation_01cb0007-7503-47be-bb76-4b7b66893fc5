<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航页数据可视化编辑</title>
    <link rel="stylesheet" href="nav/css/npve.css">
    <!-- 引入Vue.js 3 -->
    <script src="nav/js/vue.global.js"></script>
</head>
<body>
    <div id="app">
        <!-- 顶部工具栏 -->
        <header class="header">
            <h1>导航页数据可视化编辑</h1>
            <div class="toolbar">
                <input type="file" id="fileInput" @change="loadFile" accept=".json" style="display: none;">
                <button @click="triggerFileInput" class="btn btn-primary">加载JSON文件</button>
                <button @click="addRootCategory" class="btn btn-success">添加根分类</button>
                <button @click="exportData" :disabled="!hasData" class="btn btn-success">导出JSON</button>
                <button @click="clearData" :disabled="!hasData" class="btn btn-warning">清空数据</button>
                <button @click="showHelp" class="btn btn-secondary">帮助</button>
            </div>
        </header>

        <!-- 数据恢复提示 -->
        <div v-if="showRecoveryPrompt" class="recovery-prompt">
            <div class="alert alert-info">
                <p>检测到上次有未保存的修改，是否恢复？</p>
                <button @click="recoverData" class="btn btn-primary">恢复数据</button>
                <button @click="dismissRecovery" class="btn btn-secondary">忽略</button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 左侧数据树 -->
            <aside class="sidebar">
                <div class="search-box">
                    <input 
                        v-model="searchQuery" 
                        type="text" 
                        placeholder="搜索分类或网站..." 
                        class="search-input"
                    >
                </div>
                
                <div class="tree-container">
                    <div v-if="!hasData" class="empty-state">
                        <p>请先加载JSON文件</p>
                    </div>
                    
                    <!-- 递归渲染分类树 -->
                    <tree-node 
                        v-for="category in filteredCategories" 
                        :key="category.id"
                        :node="category"
                        :level="0"
                        :selected-id="selectedNodeId"
                        @select="selectNode"
                        @toggle="toggleNode"
                    ></tree-node>
                </div>
            </aside>

            <!-- 右侧编辑区域 -->
            <section class="editor-panel">
                <div v-if="!selectedNode" class="empty-editor">
                    <p>请从左侧选择一个项目进行编辑</p>
                </div>
                
                <!-- 分类编辑表单 -->
                <div v-else-if="selectedNode.type === 'category'" class="editor-form">
                    <h3>编辑分类</h3>
                    <form @submit.prevent="saveChanges">
                        <div class="form-group">
                            <label>ID:</label>
                            <input v-model="selectedNode.data.id" type="text" readonly class="form-control readonly">
                        </div>
                        <div class="form-group">
                            <label>名称:</label>
                            <input v-model="selectedNode.data.name" type="text" required class="form-control">
                        </div>
                        <div class="form-group">
                            <label>图标:</label>
                            <input v-model="selectedNode.data.icon" type="text" class="form-control">
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">保存修改</button>
                            <button type="button" @click="addChildCategory" :disabled="hasChildrenAndSites" class="btn btn-success">添加子分类</button>
                            <button type="button" @click="addSite" :disabled="hasChildrenAndSites" class="btn btn-success">添加网站</button>
                            <button type="button" @click="deleteNode" class="btn btn-danger">删除分类</button>
                        </div>
                    </form>
                </div>
                
                <!-- 网站编辑表单 -->
                <div v-else-if="selectedNode.type === 'site'" class="editor-form">
                    <h3>编辑网站</h3>
                    <form @submit.prevent="saveChanges">
                        <div class="form-group">
                            <label>ID:</label>
                            <input v-model="selectedNode.data.id" type="text" readonly class="form-control readonly">
                        </div>
                        <div class="form-group">
                            <label>名称:</label>
                            <input v-model="selectedNode.data.name" type="text" required class="form-control">
                        </div>
                        <div class="form-group">
                            <label>描述:</label>
                            <textarea v-model="selectedNode.data.description" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label>图标:</label>
                            <input v-model="selectedNode.data.icon" type="text" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>URL:</label>
                            <input v-model="selectedNode.data.url" type="url" required class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Markdown文件 (可选):</label>
                            <input v-model="selectedNode.data.markdownFile" type="text" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>标签 (用逗号分隔):</label>
                            <input v-model="tagsString" type="text" class="form-control">
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">保存修改</button>
                            <button type="button" @click="deleteNode" class="btn btn-danger">删除网站</button>
                        </div>
                    </form>
                </div>
            </section>
        </main>

        <!-- 帮助对话框 -->
        <div v-if="showHelpDialog" class="modal-overlay" @click="closeHelp">
            <div class="modal-content" @click.stop>
                <div class="modal-header">
                    <h3>使用帮助</h3>
                    <button class="modal-close" @click="closeHelp">×</button>
                </div>
                <div class="modal-body">
                    <h4>快捷键</h4>
                    <ul>
                        <li><kbd>Ctrl/Cmd + S</kbd> - 保存当前修改</li>
                        <li><kbd>Ctrl/Cmd + E</kbd> - 导出JSON文件</li>
                        <li><kbd>Delete</kbd> - 删除选中项</li>
                        <li><kbd>Escape</kbd> - 取消选择</li>
                    </ul>

                    <h4>使用说明</h4>
                    <ol>
                        <li>点击"加载JSON文件"选择要编辑的JSON文件</li>
                        <li>在左侧树状列表中点击选择要编辑的项目</li>
                        <li>在右侧表单中修改项目信息</li>
                        <li>使用"添加"按钮创建新的分类或网站</li>
                        <li>完成编辑后点击"导出JSON"保存文件</li>
                    </ol>

                    <h4>注意事项</h4>
                    <ul>
                        <li>分类下要么包含子分类，要么包含网站，不能同时包含两者</li>
                        <li>所有修改会自动保存到浏览器本地存储</li>
                        <li>导出文件后会清理本地存储</li>
                        <li>删除包含子项的分类前，请先删除所有子项</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 消息提示容器 -->
        <div class="message-container">
            <div
                v-for="message in messages"
                :key="message.id"
                :class="['message', message.type]"
            >
                {{ message.text }}
                <button class="message-close" @click="removeMessage(message.id)">×</button>
            </div>
        </div>
    </div>

    <script src="nav/js/npve.js"></script>
</body>
</html>
