{"version": "2.0", "description": "FaciShare 导航数据配置文件", "lastUpdated": "2025-08-10", "dataSources": [{"id": "foneshare", "name": "foneshare主配置", "path": "nav/data/foneshare.json", "enabled": true, "priority": 1, "description": "线上foneshare环境主配置", "tags": ["主配置"], "domains": ["oss.foneshare.cn", "127.0.0.1"]}, {"id": "firstshare", "name": "firstshare主配置", "path": "nav/data/firstshare.json", "enabled": true, "priority": 2, "description": "线下firstshare环境主配置", "tags": ["主配置"], "domains": ["oss.firstshare.cn"]}, {"id": "work-site", "name": "工作常用", "path": "nav/data/work-site.json", "enabled": true, "priority": 3, "description": "工作常用", "tags": ["work"], "domains": []}, {"id": "tools-site", "name": "实用工具", "path": "nav/data/tools-site.json", "enabled": true, "priority": 4, "description": "生产力工具", "tags": ["tools"], "domains": []}], "mergeStrategy": {"duplicateHandling": "merge", "categoryMerging": "append", "siteIdConflict": "<PERSON><PERSON><PERSON><PERSON>", "preserveOrder": true}, "validation": {"strictMode": false, "allowEmptyCategories": true, "requireUniqueIds": true}, "fallback": {"enabled": true, "defaultPath": "nav/data/firstshare.json", "onError": "fallback"}, "timeNotifications": {"enabled": false, "configPath": "data/time-notifications.json", "description": "时间范围自动提示配置", "autoLoad": false, "fallbackEnabled": false}}