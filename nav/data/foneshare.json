{"categories": [{"id": "foneshare-release", "name": "应用发布", "icon": "🌈", "children": [{"id": "foneshare-release-service", "name": "服务发布", "icon": "⭐", "sites": [{"id": "foneshare-release-service-nav-legacy", "name": "旧版导航页", "description": "当前导航页未找到跳转链接可切回旧版", "icon": "🚪", "url": "https://oss.foneshare.cn/navigation/", "tags": ["旧版导航页"]}, {"id": "foneshare-release-service-k8s", "name": "发布系统", "description": "Kubernetes应用发布管理系统", "icon": "☸️", "url": "https://k8s-app.foneshare.cn/", "markdownFile": "nav/data/docs/k8s-deploy-guide.md", "tags": ["发布", "K8s", "部署", "运维"]}, {"id": "foneshare-release-service-vm", "name": "发布系统(虚机应用)", "description": "虚拟机应用发布系统", "icon": "🖥️", "url": "https://oss.foneshare.cn/publish/deploy/", "tags": ["发布", "虚机", "部署", "运维"]}, {"id": "foneshare-release-service-jenkins", "name": "<PERSON>", "description": "持续集成和持续交付平台", "icon": "⚙️", "url": "https://oss.foneshare.cn/jenkins/", "tags": ["CI/CD", "自动化", "构建"]}, {"id": "foneshare-release-service-weex", "name": "WeexConsole", "description": "小程序发布", "icon": "📱", "url": "https://oss.foneshare.cn/weex-console/", "tags": ["weex", "console", "发布"]}]}, {"id": "foneshare-release-config", "name": "配置修改", "icon": "🔖", "sites": [{"id": "foneshare-release-config-center-new", "name": "配置中心(新版)", "description": "新版配置管理中心", "icon": "⚙️", "url": "https://console.foneshare.cn/cms/", "tags": ["配置", "管理", "新版", "运维"]}, {"id": "foneshare-release-config-center-legacy", "name": "配置中心(旧版)", "description": "旧版配置管理中心", "icon": "🔧", "url": "https://oss.foneshare.cn/cms/", "tags": ["配置", "管理", "旧版", "运维"]}, {"id": "foneshare-release-config-cloud-control", "name": "云控中心", "description": "云控中心", "icon": "☁️", "url": "https://oss.foneshare.cn/cctrl-center/", "tags": ["云控", "控制", "管理"]}, {"id": "foneshare-release-config-action-router", "name": "Action Router", "description": "灰度路由", "icon": "🔀", "url": "https://oss.foneshare.cn/router-console/", "tags": ["路由", "灰度", "管理"]}]}, {"id": "foneshare-release-monitoring", "name": "日志监控", "icon": "📊", "sites": [{"id": "foneshare-release-monitoring-clickhouse", "name": "Clickhouse", "description": "基于Clickhouse的日志中心", "icon": "📊", "url": "https://log.foneshare.cn/", "tags": ["日志", "Clickhouse", "中心", "查询"]}, {"id": "foneshare-release-monitoring-grafana-nav", "name": "Grafana日志导航", "description": "基于Grafana的日志中心", "icon": "📊", "url": "https://grafana.foneshare.cn/d/e8bf99ce-2bae-4b8c-a528-c12dced8f407/e8a786-e59bbe-e5afbc-e888aa-e9a1b5?orgId=1", "tags": ["日志", "<PERSON><PERSON>", "中心", "查询"]}, {"id": "foneshare-release-monitoring-kibana-nav", "name": "Kibana日志导航", "description": "dev/G7FsHtqfG5k2X1B5", "icon": "📊", "url": "https://oss.foneshare.cn/kibana01/app/dashboards", "tags": ["日志", "Kibana", "中心", "查询"]}, {"id": "foneshare-release-monitoring-app-log", "name": "应用日志", "description": "Grafana应用日志看板", "icon": "📝", "url": "https://grafana.foneshare.cn/d/ch-app-log/clickhouse-app-logs?orgId=1", "tags": ["日志", "<PERSON><PERSON>", "看板", "应用"]}, {"id": "foneshare-release-monitoring-foneye", "name": "蜂眼监控", "description": "蜂眼监控", "icon": "🐝", "url": "https://oss.foneshare.cn/eye/service/", "tags": ["监控", "蜂眼"]}, {"id": "foneshare-release-monitoring-terminal-availability", "name": "终端可用率", "description": "终端可用率", "icon": "📱", "url": "https://oss.foneshare.cn/eye/service/", "tags": ["监控", "终端", "可用率"]}, {"id": "foneshare-release-monitoring-client-log", "name": "客户端日志", "description": "客户端日志", "icon": "💻", "url": "https://oss.foneshare.cn/eye/service/", "tags": ["日志", "客户端"]}, {"id": "foneshare-release-monitoring-pg", "name": "PG监控", "description": "PG监控", "icon": "🐘", "url": "https://grafana.foneshare.cn/d/pg-global/postgresql-global?orgId=1", "tags": ["pg", "postgresql", "监控"]}, {"id": "foneshare-release-monitoring-app-panorama", "name": "应用全景图", "description": "应用全景图", "icon": "🗺️", "url": "https://grafana.foneshare.cn/d/-7mPcYniz5/e5ba94-e794a8-e585a8-e699af-e59bbe?orgId=1", "tags": ["监控", "应用", "全景图"]}, {"id": "foneshare-release-monitoring-node", "name": "Node节点监控", "description": "Node节点监控", "icon": "📦", "url": "https://grafana.foneshare.cn/d/9CWBz0bik2/nodee88a82-e782b9-e79b91-e68ea7?orgId=1", "tags": ["监控", "Node", "节点"]}, {"id": "foneshare-release-monitoring-error-log", "name": "错误日志", "description": "错误日志", "icon": "❌", "url": "https://grafana.foneshare.cn/d/ch-log-error/clickhouse-log-error?orgId=1", "tags": ["日志", "错误"]}, {"id": "foneshare-release-monitoring-cep-slow-log", "name": "CEP慢日志", "description": "CEP慢日志", "icon": "🐢", "url": "https://grafana.foneshare.cn/d/ch-slow-cep/clickhouse-slow-cep?orgId=1", "tags": ["日志", "CEP", "慢查询"]}, {"id": "foneshare-release-monitoring-slow-query", "name": "慢查询分析", "description": "慢查询分析", "icon": "🔍", "url": "https://grafana.foneshare.cn/d/ch-slow-log/clickhouse-slow-log?orgId=1", "tags": ["日志", "慢查询", "分析"]}, {"id": "foneshare-release-monitoring-trace-log", "name": "分布式追踪日志", "description": "分布式追踪日志", "icon": "🔗", "url": "https://grafana.foneshare.cn/d/ch-trace/clickhouse-trace?orgId=1", "tags": ["日志", "分布式", "追踪"]}, {"id": "foneshare-release-monitoring-fire-extinguisher", "name": "灭火图", "description": "灭火图", "icon": "🔥", "url": "https://grafana.foneshare.cn/d/d515ee47-4818-4340-ab9b-a42c759a1e2c/e781ad-e781ab-e585a8-e699af-e59bbe-foneshare", "tags": ["监控", "灭火图"]}]}, {"id": "foneshare-release-admin", "name": "管理后台", "icon": "🔧", "sites": [{"id": "foneshare-release-admin-filesystem", "name": "文件系统管理后台", "description": "文件系统管理后台", "icon": "🔧", "url": "https://oss.foneshare.cn/fs-stone-admin", "tags": ["stone", "文件系统", "管理后台"]}, {"id": "foneshare-release-admin-i18n", "name": "国际化管理平台", "description": "多语言国际化管理平台", "icon": "🌍", "url": "https://oss.foneshare.cn/i18n-console", "tags": ["国际化", "多语", "i18n", "管理后台"]}, {"id": "foneshare-release-admin-java", "name": "JavaConsole", "description": "JavaConsole", "icon": "☕", "url": "https://oss.foneshare.cn/javaconsole/", "tags": ["java", "console"]}, {"id": "foneshare-release-admin-paas", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "Pa<PERSON><PERSON><PERSON><PERSON>", "icon": "🔧", "url": "https://oss.foneshare.cn/paas-console/", "tags": ["paas", "console"]}, {"id": "foneshare-release-admin-openapi", "name": "openapi管理后台", "description": "互联管理后台", "icon": "🔗", "url": "https://oss.foneshare.cn/openapiadmin/admin/index.html", "tags": ["openapi", "互联", "管理后台"]}, {"id": "foneshare-release-admin-appid", "name": "AppID", "description": "AppID管理", "icon": "🆔", "url": "https://oss.foneshare.cn/pid/", "tags": ["appid", "管理"]}, {"id": "foneshare-release-admin-audit-log", "name": "审计日志查询", "description": "审计日志查询(账号请联系赵文忠)", "icon": "📜", "url": "https://audit-log.foneshare.cn/", "tags": ["审计", "日志", "查询"]}, {"id": "foneshare-release-admin-permission", "name": "权限服务", "description": "权限管理", "icon": "🔐", "url": "https://oss.foneshare.cn/rights/", "tags": ["权限", "管理后台"]}]}, {"id": "foneshare-docs", "name": "文档规范", "icon": "📚", "sites": [{"id": "foneshare-docs-release-spec", "name": "研发中心发布变更规范3.0", "description": "研发中心发布变更规范文档", "icon": "📋", "url": "https://wiki.firstshare.cn/pages/viewpage.action?pageId=103188954", "tags": ["发布", "规范", "变更", "研发"]}, {"id": "foneshare-docs-tech-spec-nav", "name": "技术规范导航页", "description": "技术规范文档导航", "icon": "🧭", "url": "https://wiki.firstshare.cn/pages/viewpage.action?pageId=176134427", "tags": ["技术", "规范", "导航", "文档"]}, {"id": "foneshare-docs-devops-intro", "name": "纷享DevOps平台介绍", "description": "纷享DevOps平台使用介绍", "icon": "🔧", "url": "https://sharecrm.feishu.cn/wiki/wikcnR0NeujciC5EEtyOtnHMJdd", "tags": ["DevOps", "平台", "介绍", "纷享"]}, {"id": "foneshare-docs-log-platform-guide", "name": "纷享日志平台-接入和查询", "description": "日志平台接入和查询指南", "icon": "📊", "url": "https://sharecrm.feishu.cn/wiki/wikcnpERP1FppAMp4s83g4ur4oc", "tags": ["日志", "平台", "接入", "查询"]}]}]}, {"id": "foneshare-ops", "name": "运维管理", "icon": "⛱️", "sites": [{"id": "foneshare-ops-falcon", "name": "falcon", "description": "运维操作平台", "icon": "🛠️", "url": "https://mon.foneshare.cn/", "tags": ["运维", "管理", "平台", "监控"]}, {"id": "foneshare-ops-cmdb", "name": "CMDB", "description": "配置管理数据库", "icon": "📊", "url": "https://cmdb.foneshare.cn/index.php/", "tags": ["配置", "数据库", "管理", "CMDB"]}, {"id": "foneshare-ops-db-query", "name": "数据库查询", "description": "数据库查询", "icon": "🗄️", "url": "https://db-query.foneshare.cn/", "tags": ["MySQL", "数据库", "管理", "工具"]}, {"id": "foneshare-ops-kubepi", "name": "KubePi", "description": "Kubernetes集群管理工具", "icon": "☸️", "url": "https://oss.foneshare.cn/kubepi", "tags": ["Kubernetes", "集群", "管理", "工具"]}, {"id": "foneshare-ops-dubbo-admin", "name": "Dubbo-Admin", "description": "Dubbo服务管理控制台", "icon": "🔗", "url": "https://dubbo.foneshare.cn/", "tags": ["Dubbo"]}, {"id": "foneshare-ops-rocketmq", "name": "RocketMQ-Console", "description": "RocketMQ控制台", "icon": "🚀", "url": "http://oss.firstshare.cn/rmq-console-112/", "tags": ["RocketMQ", "消息队列", "监控", "MQ"]}, {"id": "foneshare-ops-kafka-manager", "name": "<PERSON><PERSON><PERSON>-Manager", "description": "Kafka消息队列管理工具(fsdevops)", "icon": "📨", "url": "https://oss.foneshare.cn/kafka-manager", "tags": ["Kafka", "消息队列", "管理", "fsdevops"]}, {"id": "foneshare-ops-kafka-redpanda", "name": "Kafka-RedPanda", "description": "Kafka日志中心", "icon": "🐼", "url": "https://redpanda-console.foneshare.cn/", "tags": ["Kafka", "日志", "Red<PERSON>anda"]}, {"id": "foneshare-ops-cerebro", "name": "cerebro", "description": "Elasticsearch集群管理工具", "icon": "🍥", "url": "https://oss.foneshare.cn/cerebro/", "tags": ["Elasticsearch", "集群", "管理", "工具"]}, {"id": "foneshare-ops-kafka-redpanda-audit", "name": "Kafka-RedPanda-Audit", "description": "Kafka 审计日志中心", "icon": "🐼", "url": "https://redpanda-console-log.foneshare.cn/", "tags": ["kafak", "审计日志"]}, {"id": "foneshare-ops-xxl-job", "name": "XXL-Job", "description": "分布式任务", "icon": "⏰", "url": "https://oss.foneshare.cn/job/", "tags": ["job", "任务"]}, {"id": "foneshare-ops-xshell", "name": "Xshell", "description": "Xshell", "icon": "💻", "url": "https://oss.foneshare.cn/xshell", "tags": ["xshell"]}]}, {"id": "foneshare-cloud", "name": "多云管理", "icon": "🌐", "children": [{"id": "foneshare-cloud-saas", "name": "SaaS", "icon": "☁️", "children": [{"id": "foneshare-cloud-saas-huaweicloud", "name": "华为云", "icon": "☁️", "sites": [{"id": "foneshare-cloud-saas-huaweicloud-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://hwc-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-saas-huaweicloud-kibana", "name": "Kibana", "description": "Kibana日志查询与可视化分析平台。", "icon": "🔍", "url": "https://hwc-prod.foneshare.cn/kibana4318", "tags": ["kibana", "log", "dashboard"]}, {"id": "foneshare-cloud-saas-huaweicloud-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://hwc-prod.foneshare.cn/grafana", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-saas-huaweicloud-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://oss.foneshare.cn/javaconsole-hw/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-saas-huaweicloud-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://hwc-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-saas-huaweicloud-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台，用于消息队列监控。", "icon": "🚀", "url": "https://hwc-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-saas-huaweicloud-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://hw-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-saas-huaweicloud-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://hwc-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-saas-huaweicloud-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://hwc-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-saas-ale", "name": "阿里云", "icon": "☁️", "sites": [{"id": "foneshare-cloud-saas-ale-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://ale-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-saas-ale-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://ale-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-saas-ale-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://ale-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-saas-ale-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-saas-ale-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://ale-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-saas-ale-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://ale-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-saas-ale-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://ale-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-saas-ale-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://ale-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-saas-eu", "name": "亚马逊法兰克福", "icon": "☁️", "sites": [{"id": "foneshare-cloud-saas-eu-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://hws-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-saas-eu-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://hws-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-saas-eu-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://hws-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-saas-eu-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://hws-prod.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-saas-eu-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://hws-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-saas-eu-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://hws-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-saas-eu-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://hws-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-saas-eu-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://hws-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-saas-hk", "name": "亚马逊中国香港", "icon": "☁️", "sites": [{"id": "foneshare-cloud-saas-hk-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://ksc-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-saas-hk-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://ksc-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-saas-hk-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://ksc-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-saas-hk-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-saas-hk-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://ksc-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-saas-hk-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://ksc-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-saas-hk-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://ksc-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-saas-hk-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://ksc-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-saas-sea", "name": "亚马逊东南亚新加坡", "icon": "☁️", "sites": [{"id": "foneshare-cloud-saas-sea-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://forsharecrm-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-saas-sea-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://forsharecrm-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-saas-sea-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://forsharecrm-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-saas-sea-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-saas-sea-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://forsharecrm-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-saas-sea-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://forsharecrm-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-saas-sea-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://forsharecrm-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-saas-sea-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://forsharecrm-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-saas-na", "name": "亚马逊北美北加州", "icon": "☁️", "sites": [{"id": "foneshare-cloud-saas-na-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://kemaicrm-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-saas-na-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://kemaicrm-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-saas-na-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://kemaicrm-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-saas-na-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-saas-na-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://kemaicrm-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-saas-na-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://kemaicrm-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-saas-na-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://kemaicrm-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-saas-na-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://kemaicrm-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}]}, {"id": "foneshare-cloud-private", "name": "应用专属", "icon": "🏢", "children": [{"id": "foneshare-cloud-private-sbt", "name": "双胞胎", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-sbt-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://sbt-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-sbt-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://sbt-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-sbt-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://sbt-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-sbt-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://sbt-prod.foneshare.cn/grafana", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-sbt-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://sbt-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-sbt-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://sbt-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-sbt-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://sbt-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-sbt-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://sbt-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-unicloudea", "name": "紫光云", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-unicloudea-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://ucd-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-unicloudea-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://ucd-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-unicloudea-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://ucd-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-unicloudea-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-unicloudea-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://ucd-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-unicloudea-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://ucd-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-unicloudea-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://ucd-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-unicloudea-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://ucd-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-xjgc", "name": "许继云", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-xjgc-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://xjgc-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-xjgc-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://xjgc-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-xjgc-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://xjgc-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-xjgc-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://xjgc-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-xjgc-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://xjgc-prod.foneshare.cn/grafana", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-xjgc-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://xjgc-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-xjgc-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://xjgc-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-xjgc-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://xjgc-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-hisense", "name": "海信云", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-hisense-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://hisense-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-hisense-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://hisense-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-hisense-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://hisense-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-hisense-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://hisense-prod.foneshare.cn/grafana", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-hisense-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://hisense-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-hisense-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://hisense-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-hisense-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://hisense-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-hisense-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://hisense-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-chinatower", "name": "铁塔云", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-chinatower-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://chinatower-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-chinatower-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://chinatower-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-chinatower-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://chinatower-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-chinatower-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://chinatower-prod.foneshare.cn/grafana", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-chinatower-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://chinatower-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-chinatower-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://chinatower-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-chinatower-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://chinatower-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-chinatower-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://chinatower-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-privates-msv", "name": "蒙牛云", "icon": "🏢", "sites": [{"id": "foneshare-cloud-privates-msv-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://mengniu-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-privates-msv-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://mengniu-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-privates-msv-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://mengniu-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-privates-msv-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，数据源需选择mengniu-prometheus。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-privates-msv-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://mengniu-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-privates-msv-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://mengniu-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-privates-msv-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://mengniu-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-privates-msv-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://mengniu-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-hevision", "name": "何氏眼科", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-hevision-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://hsyk-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-hevision-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://hsyk-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-hevision-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://hsyk-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-hevision-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，数据源需选择heyk-prometheus。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-hevision-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://hsyk-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-hevision-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://hsyk-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-hevision-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://hsyk-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-hevision-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://hsyk-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-wzz", "name": "伍子醉", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-wzz-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://wuzizui99-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-wzz-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://wuzizui99-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-wzz-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://wuzizui99-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-wzz-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-wzz-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://wuzizui99-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-wzz-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://wuzizui99-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-wzz-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://wuzizui99-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-wzz-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://wuzizui99-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-iflytek", "name": "科大讯飞", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-iflytek-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://iflytek-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-iflytek-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://iflytek-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-iflytek-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://iflytek-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-iflytek-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-iflytek-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://iflytek-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-iflytek-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://iflytek-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-iflytek-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://iflytek-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-iflytek-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://iflytek-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-hexagonmi", "name": "海克思康", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-hexagonmi-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://hexagonmi-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-hexagonmi-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://hexagonmi-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-hexagonmi-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://hexagonmi-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-hexagonmi-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-hexagonmi-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://hexagonmi-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-hexagonmi-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://hexagonmi-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-hexagonmi-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://hexagonmi-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-hexagonmi-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://hexagonmi-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-yangnongchem", "name": "扬农化工", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-yangnongchem-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://yangnongchem-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-yangnongchem-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://yangnongchem-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-yangnongchem-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://yangnongchem-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-yangnongchem-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-yangnongchem-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://yangnongchem-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-yangnongchem-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://yangnongchem-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-yangnongchem-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://yangnongchem-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-yangnongchem-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://yangnongchem-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-teleagi", "name": "电信智控", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-teleagi-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://teleagi-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-teleagi-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://teleagi-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-teleagi-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://teleagi-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-teleagi-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-teleagi-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://teleagi-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-teleagi-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://teleagi-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-teleagi-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://teleagi-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-teleagi-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://teleagi-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-cpgc", "name": "中船(宁夏)", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-cpgc-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://cpgc-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-cpgc-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://cpgc-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-cpgc-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://cpgc-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-cpgc-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-cpgc-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://cpgc-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-cpgc-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://cpgc-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-cpgc-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://cpgc-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-cpgc-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://cpgc-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-wingd", "name": "中船(法兰克福)", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-wingd-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://wingd-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-wingd-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://wingd-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-wingd-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://wingd-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-wingd-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-wingd-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://wingd-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-wingd-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://wingd-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-wingd-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://wingd-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-wingd-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://wingd-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-kehua", "name": "科华云", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-kehua-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://kehua-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-kehua-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://kehua-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-kehua-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://kehua-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-kehua-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-kehua-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://kehua-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-kehua-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://kehua-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-kehua-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://kehua-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-kehua-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://kehua-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-jingbo", "name": "京博云", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-jingbo-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://jingbo-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-jingbo-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://jingbo-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-jingbo-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://jingbo-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-jingbo-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-jingbo-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://jingbo-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-jingbo-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://jingbo-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-jingbo-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://jingbo-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-jingbo-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://jingbo-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-private-tbea", "name": "特变电工", "icon": "🏢", "sites": [{"id": "foneshare-cloud-private-tbea-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://tbea-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-private-tbea-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://tbea-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-private-tbea-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://tbea-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-private-tbea-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-private-tbea-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://tbea-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-private-tbea-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://tbea-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-private-tbea-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://tbea-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-private-tbea-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://tbea-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}]}, {"id": "foneshare-cloud-other", "name": "其他", "icon": "📦", "children": [{"id": "foneshare-cloud-other-cloudmodel", "name": "模版云", "icon": "📦", "sites": [{"id": "foneshare-cloud-other-cloudmodel-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://cloudmodel-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-other-cloudmodel-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://cloudmodel-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-other-cloudmodel-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://cloudmodel-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-other-cloudmodel-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-other-cloudmodel-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://cloudmodel-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-other-cloudmodel-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://cloudmodel-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-other-cloudmodel-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://cloudmodel-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-other-cloudmodel-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://cloudmodel-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-other-forceecrm", "name": "复制云(招商局)", "icon": "📦", "sites": [{"id": "foneshare-cloud-other-forceecrm-cms", "name": "配置中心", "description": "系统的集中化配置管理中心。", "icon": "⚙️", "url": "https://forceecrm-prod.foneshare.cn/cms/", "tags": ["cms", "config", "admin"]}, {"id": "foneshare-cloud-other-forceecrm-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://forceecrm-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-other-forceecrm-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://forceecrm-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-other-forceecrm-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://forceecrm-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-other-forceecrm-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-other-forceecrm-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://forceecrm-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-other-forceecrm-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://forceecrm-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-other-forceecrm-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://forceecrm-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-other-forceecrm-cctrlCenter", "name": "云控", "description": "云端集中控制与管理中心。", "icon": "📡", "url": "https://forceecrm-prod.foneshare.cn/cctrl-center/config/show", "tags": ["control", "center", "config"]}, {"id": "foneshare-cloud-other-forceecrm-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://forceecrm-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-other-ucd", "name": "性能测试云", "icon": "📦", "sites": [{"id": "foneshare-cloud-other-ucd-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://ucd-test.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-other-ucd-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://ucd-test.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-other-ucd-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://ucd-test-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-other-ucd-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-other-ucd-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://ucd-test.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-other-ucd-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://ucd-test.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-other-ucd-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://ucd-test-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-other-ucd-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://ucdtest-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}, {"id": "foneshare-cloud-other-allink8s", "name": "allink8s", "icon": "📦", "sites": [{"id": "foneshare-cloud-other-allink8s-javaConsole", "name": "JavaConsole", "description": "Java应用管理的后台控制台。", "icon": "☕️", "url": "https://allink8s-prod.foneshare.cn/javaconsole/", "tags": ["java", "console", "admin"]}, {"id": "foneshare-cloud-other-allink8s-rocketMqConsole", "name": "RocketMQ-Console", "description": "Apache RocketMQ的Web管理控制台。", "icon": "🚀", "url": "https://allink8s-prod.foneshare.cn/rmq-console/", "tags": ["rocketmq", "mq", "console"]}, {"id": "foneshare-cloud-other-allink8s-dubboAdmin", "name": "Dubbo-Admin", "description": "Apache Dubbo服务治理与管理控制台。", "icon": "🦅", "url": "https://allink8s-prod-dubbo.foneshare.cn/", "tags": ["dubbo", "rpc", "admin"]}, {"id": "foneshare-cloud-other-allink8s-grafana", "name": "<PERSON><PERSON>", "description": "Grafana监控看板，用于JVM及系统性能指标可视化。", "icon": "📈", "url": "https://oss.foneshare.cn/grafana01", "tags": ["grafana", "dashboard", "monitoring"]}, {"id": "foneshare-cloud-other-allink8s-xxlJob", "name": "XXL-Job", "description": "一个轻量级分布式任务调度平台。", "icon": "⏰", "url": "https://allink8s-prod.foneshare.cn/job/", "tags": ["xxl-job", "scheduler", "task"]}, {"id": "foneshare-cloud-other-allink8s-paasConsole", "name": "Pa<PERSON><PERSON><PERSON><PERSON>", "description": "PaaS平台管理后台，提供平台级服务管理。", "icon": "🏗️", "url": "https://allink8s-prod.foneshare.cn/paas-console/", "tags": ["paas", "console", "platform"]}, {"id": "foneshare-cloud-other-allink8s-clickhouse", "name": "Clickhouse", "description": "基于ClickHouse的日志分析与查询中心。", "icon": "⚡️", "url": "https://allink8s-prod-log.foneshare.cn/", "tags": ["clickhouse", "log", "data"]}, {"id": "foneshare-cloud-other-allink8s-redpandaConsole", "name": "RedPanda-<PERSON><PERSON><PERSON>", "description": "RedPanda（兼容Kafka）消息队列的管理与监控控制台。", "icon": "🐼", "url": "https://allink8s-prod-kafka.foneshare.cn/", "tags": ["<PERSON>pan<PERSON>", "kafka", "console"]}]}]}]}, {"id": "foneshare-sre", "name": "巡检值班", "icon": "📁", "children": [{"id": "foneshare-sre-dashboards", "name": "核心看板", "icon": "📊", "sites": [{"id": "foneshare-sre-dashboard-global", "name": "全局核心看板", "description": "全局核心看板", "icon": "📊", "url": "https://grafana.foneshare.cn/d/D5Ix26nZz/fs-core", "tags": ["监控", "核心", "看板"]}, {"id": "foneshare-sre-dashboard-cep", "name": "CEP监控", "description": "CEP监控", "icon": "📈", "url": "https://grafana.foneshare.cn/d/log-cep/log-cep", "tags": ["监控", "CEP"]}, {"id": "foneshare-sre-dashboard-error-log", "name": "错误日志", "description": "错误日志", "icon": "❌", "url": "https://grafana.foneshare.cn/d/ch-log-error/clickhouse-log-error?orgId=1", "tags": ["日志", "错误"]}, {"id": "foneshare-sre-dashboard-pg-load", "name": "PG库负载", "description": "postgresql全局负载情况", "icon": "🐘", "url": "https://grafana.foneshare.cn/d/pg-global/postgresql-global", "tags": ["监控", "PG", "负载"]}, {"id": "foneshare-sre-dashboard-slow-query", "name": "Sql慢查询分析", "description": "Sql慢查询分析", "icon": "🔍", "url": "https://grafana.foneshare.cn/d/ch-slow-log/clickhouse-slow-log?orgId=1", "tags": ["日志", "慢查询", "分析"]}, {"id": "foneshare-sre-dashboard-mq-blockage", "name": "MQ阻塞", "description": "MQ阻塞", "icon": "🚧", "url": "https://grafana.foneshare.cn/d/Lb9O2S-iz/rocketmq", "tags": ["监控", "MQ", "阻塞"]}, {"id": "foneshare-sre-dashboard-e2e", "name": "端到端全局看板", "description": "端到端全局看板", "icon": "📊", "url": "https://grafana.foneshare.cn/d/Q0XU_SQnz/global-core-dashboard", "tags": ["监控", "端到端", "看板"]}, {"id": "foneshare-sre-dashboard-k8s-cluster", "name": "k8s集群状态", "description": "k8s集群状态", "icon": "☸️", "url": "https://grafana.foneshare.cn/d/9CWBz0bik/1-node-exporter-for-prometheus-dashboard-cn-v20201010", "tags": ["监控", "k8s", "集群"]}, {"id": "foneshare-sre-dashboard-abu-core", "name": "ABU核心监控", "description": "ABU核心监控", "icon": "📊", "url": "https://grafana.foneshare.cn/d/kc6Dozt4k/abu?orgId=1&var-ea=ylspjt&var-ea=yqsl100&var-ea=orion2020&var-ea=ruijie2021&from=now-12h&to=now", "tags": ["监控", "ABU", "核心"]}]}, {"id": "foneshare-sre-manuals", "name": "修炼手册", "icon": "📊", "sites": [{"id": "foneshare-sre-manual-on-call", "name": "巡检值班手册", "description": "巡检值班手册", "icon": "📖", "url": "https://wiki.firstshare.cn/pages/viewpage.action?pageId=179250917", "tags": ["文档", "巡检", "值班"]}, {"id": "foneshare-sre-manual-monitoring-service", "name": "监控服务巡检手册", "description": "巡检值班老中医速成手册", "icon": "👨‍⚕️", "url": "https://a5sukomglt.feishu.cn/docs/doccn6xRlYOHCGsSBIQftgMUmkg", "tags": ["文档", "巡检", "监控"]}]}]}, {"id": "foneshare-code", "name": "代码管理", "icon": "🌀", "sites": [{"id": "foneshare-code-gitlab", "name": "GitLab", "description": "代码版本管理仓库", "icon": "🦊", "url": "https://git.firstshare.cn/", "markdownFile": "nav/data/docs/git-guide.md", "tags": ["Git", "代码", "版本控制", "仓库"]}, {"id": "foneshare-code-sourcegraph", "name": "SourceGraph", "description": "代码搜索和浏览工具", "icon": "🔍", "url": "https://sourcegraph.firstshare.cn/", "tags": ["代码搜索", "浏览", "SourceGraph", "Git"]}, {"id": "foneshare-code-maven", "name": "<PERSON><PERSON>", "description": "Maven依赖管理仓库", "icon": "📦", "url": "https://maven.firstshare.cn/", "tags": ["<PERSON><PERSON>", "依赖", "仓库", "Artifactory"]}, {"id": "foneshare-code-sonarqube", "name": "SonarQube", "description": "代码质量扫描工具", "icon": "🔎", "url": "https://oss.firstshare.cn/sonarqube/", "tags": ["代码扫描", "质量", "SonarQube", "检查"]}, {"id": "foneshare-code-scaffold-springboot", "name": "SpringBoot脚手架", "description": "项目脚手架生成工具", "icon": "🏗️", "url": "https://oss.firstshare.cn/starter/", "tags": ["脚手架", "项目", "生成", "模板"]}, {"id": "foneshare-code-parent-pom-diff", "name": "父POM版本", "description": "父POM组件版本对比工具", "icon": "📋", "url": "https://git.firstshare.cn/JavaCommon/parent-pom/-/blob/master/version-diff.md", "tags": ["POM", "版本", "组件", "对比"]}]}]}