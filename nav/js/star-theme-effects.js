/**
 * 星月流光蓝主题动态特效
 * Star Moonlight Blue Theme Dynamic Effects
 */

class StarThemeEffects {
    constructor() {
        this.isActive = false;
        this.starfieldContainer = null;
        this.shootingStars = [];
        this.constellation = null;
        this.moon = null;
        this.animationFrameId = null;
        
        // 配置参数
        this.config = {
            starCount: 200,
            shootingStarInterval: 6000,
            shootingStarProbability: 0.4,
            constellationTwinkleSpeed: 3000,
            moonPulseSpeed: 8000
        };
        
        this.init();
    }
    
    init() {
        // 监听主题变化
        this.watchThemeChanges();
        
        // 检查当前主题
        if (this.isStarThemeActive()) {
            this.activate();
        }
    }
    
    watchThemeChanges() {
        // 使用MutationObserver监听data-theme属性变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                    if (this.isStarThemeActive()) {
                        this.activate();
                    } else {
                        this.deactivate();
                    }
                }
            });
        });
        
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['data-theme']
        });
    }
    
    isStarThemeActive() {
        return document.body.getAttribute('data-theme') === 'star-moonlight-blue';
    }
    
    activate() {
        if (this.isActive) return;
        
        this.isActive = true;
        console.log('Activating Star Moonlight Blue theme effects');
        
        // 创建特效容器
        this.createEffectContainers();
        
        // 生成星空
        this.createStarfield();
        
        // 创建月亮
        this.createMoon();
        
        // 创建星座
        this.createConstellation();
        
        // 启动流星生成
        this.startShootingStars();
        
        // 添加鼠标视差效果
        this.addParallaxEffect();
        
        // 启动动画循环
        this.startAnimationLoop();
    }
    
    deactivate() {
        if (!this.isActive) return;
        
        this.isActive = false;
        console.log('Deactivating Star Moonlight Blue theme effects');
        
        // 清理所有特效元素
        this.cleanup();
    }
    
    createEffectContainers() {
        // 创建星空容器
        this.starfieldContainer = document.createElement('div');
        this.starfieldContainer.className = 'star-theme-starfield';
        this.starfieldContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -10;
            pointer-events: none;
        `;
        document.body.appendChild(this.starfieldContainer);
        
        // 创建月亮容器
        this.moon = document.createElement('div');
        this.moon.className = 'star-theme-moon';
        this.moon.style.cssText = `
            position: fixed;
            top: 6%;
            right: 10%;
            width: 150px;
            height: 150px;
            background: radial-gradient(circle at 30% 25%, 
                #ffffff 0%, 
                #f8f8ff 15%,
                #e6f3ff 35%, 
                rgba(240, 248, 255, 0.95) 60%,
                rgba(176, 196, 222, 0.8) 100%);
            border-radius: 50%;
            z-index: -8;
            box-shadow: 
                0 0 60px rgba(255, 255, 255, 0.8),
                0 0 120px rgba(0, 191, 255, 0.6),
                0 0 180px rgba(30, 144, 255, 0.4);
            animation: star-moon-glow 8s ease-in-out infinite;
            pointer-events: none;
        `;
        
        // 添加月面纹理
        const moonTexture = document.createElement('div');
        moonTexture.style.cssText = `
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: 
                radial-gradient(circle at 45% 20%, rgba(200, 200, 200, 0.4) 0%, transparent 25%),
                radial-gradient(circle at 60% 40%, rgba(180, 180, 180, 0.3) 0%, transparent 20%),
                radial-gradient(circle at 25% 60%, rgba(190, 190, 190, 0.3) 0%, transparent 15%);
        `;
        this.moon.appendChild(moonTexture);
        
        document.body.appendChild(this.moon);
        
        // 创建星座容器
        this.constellation = document.createElement('div');
        this.constellation.className = 'star-theme-constellation';
        this.constellation.style.cssText = `
            position: fixed;
            top: 12%;
            left: 8%;
            width: 300px;
            height: 200px;
            z-index: -7;
            opacity: 0.6;
            pointer-events: none;
        `;
        this.createConstellationSVG();
        document.body.appendChild(this.constellation);
        
        // 添加动画样式
        this.addAnimationStyles();
    }
    
    createStarfield() {
        // 清空现有星星
        this.starfieldContainer.innerHTML = '';
        
        for (let i = 0; i < this.config.starCount; i++) {
            const star = document.createElement('div');
            star.className = 'star-theme-star';
            
            // 随机大小
            const size = Math.random();
            let starClass, starSize, shadowIntensity;
            
            if (size < 0.6) {
                starClass = 'small';
                starSize = '1px';
                shadowIntensity = '2px';
            } else if (size < 0.85) {
                starClass = 'medium';
                starSize = '2px';
                shadowIntensity = '6px';
            } else {
                starClass = 'large';
                starSize = '3px';
                shadowIntensity = '10px';
            }
            
            star.style.cssText = `
                position: absolute;
                width: ${starSize};
                height: ${starSize};
                background: #fff;
                border-radius: 50%;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                box-shadow: 0 0 ${shadowIntensity} #00bfff;
                animation: star-twinkle ${2 + Math.random() * 2}s ease-in-out infinite;
                animation-delay: ${Math.random() * 3}s;
            `;
            
            this.starfieldContainer.appendChild(star);
        }
    }
    
    createConstellationSVG() {
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', '100%');
        svg.setAttribute('height', '100%');
        svg.setAttribute('viewBox', '0 0 300 200');
        
        // 星座连线
        const lines = [
            { x1: 50, y1: 50, x2: 100, y2: 30 },
            { x1: 100, y1: 30, x2: 150, y2: 60 },
            { x1: 150, y1: 60, x2: 200, y2: 40 },
            { x1: 200, y1: 40, x2: 180, y2: 80 },
            { x1: 180, y1: 80, x2: 120, y2: 90 },
            { x1: 120, y1: 90, x2: 50, y2: 50 },
            { x1: 100, y1: 30, x2: 120, y2: 90 }
        ];
        
        lines.forEach(line => {
            const lineElement = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            lineElement.setAttribute('x1', line.x1);
            lineElement.setAttribute('y1', line.y1);
            lineElement.setAttribute('x2', line.x2);
            lineElement.setAttribute('y2', line.y2);
            lineElement.setAttribute('stroke', '#00bfff');
            lineElement.setAttribute('stroke-width', '1.5');
            lineElement.setAttribute('opacity', '0.7');
            lineElement.style.animation = 'constellation-glow 6s ease-in-out infinite';
            svg.appendChild(lineElement);
        });
        
        // 星座星星
        const stars = [
            { x: 50, y: 50 },
            { x: 100, y: 30 },
            { x: 150, y: 60 },
            { x: 200, y: 40 },
            { x: 180, y: 80 },
            { x: 120, y: 90 }
        ];
        
        stars.forEach(star => {
            const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            circle.setAttribute('cx', star.x);
            circle.setAttribute('cy', star.y);
            circle.setAttribute('r', '2.5');
            circle.setAttribute('fill', '#fff');
            circle.setAttribute('opacity', '0.8');
            circle.style.animation = 'constellation-pulse 4s ease-in-out infinite';
            svg.appendChild(circle);
        });
        
        this.constellation.appendChild(svg);
    }
    
    startShootingStars() {
        const createShootingStar = () => {
            if (!this.isActive) return;
            
            if (Math.random() < this.config.shootingStarProbability) {
                const shootingStar = document.createElement('div');
                shootingStar.className = 'star-theme-shooting-star';
                
                // 随机起始位置
                const startTop = Math.random() * 40;
                const startLeft = 80 + Math.random() * 20;
                const duration = 3 + Math.random() * 2;
                
                shootingStar.style.cssText = `
                    position: fixed;
                    width: 3px;
                    height: 3px;
                    background: radial-gradient(circle, #fff 0%, #00bfff 50%, transparent 100%);
                    border-radius: 50%;
                    top: ${startTop}%;
                    left: ${startLeft}%;
                    z-index: -6;
                    pointer-events: none;
                    animation: shooting-star-flight ${duration}s linear;
                `;
                
                // 添加尾迹
                const trail = document.createElement('div');
                trail.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 80px;
                    height: 2px;
                    background: linear-gradient(to left, 
                        rgba(255, 255, 255, 0.8) 0%,
                        rgba(0, 191, 255, 0.6) 30%,
                        rgba(30, 144, 255, 0.4) 60%,
                        transparent 100%);
                    transform-origin: 0 50%;
                    transform: rotate(45deg);
                    border-radius: 50px;
                `;
                shootingStar.appendChild(trail);
                
                document.body.appendChild(shootingStar);
                this.shootingStars.push(shootingStar);
                
                // 移除流星
                setTimeout(() => {
                    if (shootingStar.parentNode) {
                        shootingStar.parentNode.removeChild(shootingStar);
                    }
                    this.shootingStars = this.shootingStars.filter(s => s !== shootingStar);
                }, duration * 1000);
            }
            
            // 安排下一次流星
            setTimeout(createShootingStar, this.config.shootingStarInterval);
        };
        
        // 立即开始第一颗流星
        setTimeout(createShootingStar, 1000);
    }
    
    addParallaxEffect() {
        this.mouseMoveHandler = (e) => {
            if (!this.isActive) return;
            
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;
            
            // 月亮视差
            if (this.moon) {
                this.moon.style.transform = `translate(${mouseX * 15}px, ${mouseY * 15}px)`;
            }
            
            // 星座视差
            if (this.constellation) {
                this.constellation.style.transform = `translate(${mouseX * -8}px, ${mouseY * -8}px)`;
            }
        };
        
        document.addEventListener('mousemove', this.mouseMoveHandler);
    }
    
    startAnimationLoop() {
        // 这里可以添加需要持续更新的动画效果
        const animate = () => {
            if (!this.isActive) return;
            
            // 可以在这里添加复杂的动画逻辑
            
            this.animationFrameId = requestAnimationFrame(animate);
        };
        
        animate();
    }
    
    addAnimationStyles() {
        if (document.getElementById('star-theme-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'star-theme-styles';
        style.textContent = `
            @keyframes star-twinkle {
                0%, 100% { 
                    opacity: 0.4; 
                    transform: scale(1); 
                    filter: brightness(1);
                }
                25% { 
                    opacity: 0.8; 
                    transform: scale(1.3); 
                    filter: brightness(1.5);
                }
                50% { 
                    opacity: 1; 
                    transform: scale(1.5); 
                    filter: brightness(2);
                }
                75% { 
                    opacity: 0.8; 
                    transform: scale(1.2); 
                    filter: brightness(1.3);
                }
            }
            
            @keyframes star-moon-glow {
                0%, 100% { 
                    transform: scale(1);
                    box-shadow: 
                        0 0 60px rgba(255, 255, 255, 0.8),
                        0 0 120px rgba(0, 191, 255, 0.6),
                        0 0 180px rgba(30, 144, 255, 0.4);
                }
                50% { 
                    transform: scale(1.08);
                    box-shadow: 
                        0 0 80px rgba(255, 255, 255, 1),
                        0 0 160px rgba(0, 191, 255, 0.8),
                        0 0 240px rgba(30, 144, 255, 0.6);
                }
            }
            
            @keyframes shooting-star-flight {
                0% {
                    transform: translate(0, 0) rotate(45deg);
                    opacity: 0;
                }
                5% {
                    opacity: 1;
                }
                95% {
                    opacity: 0.8;
                }
                100% {
                    transform: translate(300px, 300px) rotate(45deg);
                    opacity: 0;
                }
            }
            
            @keyframes constellation-glow {
                0%, 100% { 
                    opacity: 0.7; 
                    filter: drop-shadow(0 0 3px #1e90ff);
                }
                50% { 
                    opacity: 1; 
                    filter: drop-shadow(0 0 6px #00bfff);
                }
            }
            
            @keyframes constellation-pulse {
                0%, 100% { 
                    opacity: 0.8; 
                    filter: drop-shadow(0 0 4px #00bfff);
                }
                50% { 
                    opacity: 1; 
                    filter: drop-shadow(0 0 8px #1e90ff);
                }
            }
        `;
        
        document.head.appendChild(style);
    }
    
    cleanup() {
        // 移除特效容器
        if (this.starfieldContainer && this.starfieldContainer.parentNode) {
            this.starfieldContainer.parentNode.removeChild(this.starfieldContainer);
            this.starfieldContainer = null;
        }
        
        if (this.moon && this.moon.parentNode) {
            this.moon.parentNode.removeChild(this.moon);
            this.moon = null;
        }
        
        if (this.constellation && this.constellation.parentNode) {
            this.constellation.parentNode.removeChild(this.constellation);
            this.constellation = null;
        }
        
        // 清理流星
        this.shootingStars.forEach(star => {
            if (star.parentNode) {
                star.parentNode.removeChild(star);
            }
        });
        this.shootingStars = [];
        
        // 移除事件监听器
        if (this.mouseMoveHandler) {
            document.removeEventListener('mousemove', this.mouseMoveHandler);
            this.mouseMoveHandler = null;
        }
        
        // 停止动画循环
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
        
        // 移除样式
        const styleElement = document.getElementById('star-theme-styles');
        if (styleElement) {
            styleElement.parentNode.removeChild(styleElement);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 确保在其他脚本加载完成后再初始化
    setTimeout(() => {
        window.starThemeEffects = new StarThemeEffects();
    }, 100);
}); 