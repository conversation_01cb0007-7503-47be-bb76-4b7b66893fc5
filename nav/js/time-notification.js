/**
 * 时间范围提示管理器
 * 支持在指定时间范围内显示首次提示
 * 支持从配置文件动态加载配置
 */
class TimeRangeNotificationManager {
    constructor() {
        // 初始化基础配置
        this.config = {};
        this.isEnabled = false;
        this.notifications = [];
        this.checkInterval = null;
        this.checkIntervalMs = 30000; // 30秒检查一次
        this.configLoaded = false;

        // 本地存储键前缀
        this.storagePrefix = 'navsphere-time-notification-';

        console.log('TimeRangeNotificationManager initializing...');

        // 异步加载配置
        this.loadConfig().then(() => {
            console.log('TimeRangeNotificationManager initialized', {
                enabled: this.isEnabled,
                notifications: this.notifications.length
            });

            if (this.isEnabled) {
                this.startChecking();
            }
        }).catch(error => {
            console.error('Failed to load time notification config:', error);
            this.loadFallbackConfig();
        });
    }

    /**
     * 加载配置文件
     */
    async loadConfig() {
        try {
            // 首先加载应用配置以获取时间通知配置路径
            const appConfigResponse = await fetch('nav/data/appconfig.json');
            if (!appConfigResponse.ok) {
                throw new Error(`Failed to load app config: ${appConfigResponse.status}`);
            }

            const appConfig = await appConfigResponse.json();
            const timeNotificationConfig = appConfig.timeNotifications;

            if (!timeNotificationConfig || !timeNotificationConfig.enabled) {
                console.log('Time notifications disabled in app config');
                this.isEnabled = false;
                this.configLoaded = true;
                return;
            }

            // 加载时间通知配置文件
            const configPath = timeNotificationConfig.configPath || 'data/time-notifications.json';
            const configResponse = await fetch(configPath);

            if (!configResponse.ok) {
                throw new Error(`Failed to load time notification config: ${configResponse.status}`);
            }

            const timeConfig = await configResponse.json();

            // 应用配置
            this.config = timeConfig.config || {};
            this.isEnabled = this.config.enabled !== false;
            this.notifications = timeConfig.notifications || [];
            this.checkIntervalMs = this.config.checkInterval || 30000;
            this.configLoaded = true;

            console.log('Time notification config loaded successfully', {
                configPath,
                notificationCount: this.notifications.length,
                enabled: this.isEnabled
            });

        } catch (error) {
            console.error('Error loading time notification config:', error);
            throw error;
        }
    }

    /**
     * 加载降级配置（当配置文件加载失败时）
     */
    loadFallbackConfig() {
        console.log('Loading fallback time notification config...');

        // 使用原有的配置作为降级
        const fallbackConfig = window.NavSphereConfig?.timeRangeNotification;
        if (fallbackConfig) {
            this.config = fallbackConfig;
            this.isEnabled = this.config.enabled !== false;
            this.notifications = this.config.notifications || [];
            this.configLoaded = true;

            console.log('Fallback config loaded', {
                enabled: this.isEnabled,
                notifications: this.notifications.length
            });

            if (this.isEnabled) {
                this.startChecking();
            }
        } else {
            console.warn('No fallback config available, time notifications disabled');
            this.isEnabled = false;
            this.configLoaded = true;
        }
    }

    /**
     * 重新加载配置
     */
    async reloadConfig() {
        console.log('Reloading time notification config...');

        // 停止当前检查
        this.stopChecking();

        try {
            await this.loadConfig();

            if (this.isEnabled) {
                this.startChecking();
            }

            console.log('Config reloaded successfully');
            return true;
        } catch (error) {
            console.error('Failed to reload config:', error);
            this.loadFallbackConfig();
            return false;
        }
    }

    /**
     * 开始定时检查
     */
    startChecking() {
        // 立即检查一次
        this.checkNotifications();
        
        // 设置定时检查
        this.checkInterval = setInterval(() => {
            this.checkNotifications();
        }, this.checkIntervalMs);
        
        console.log('Time range notification checking started');
    }
    
    /**
     * 停止定时检查
     */
    stopChecking() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
            console.log('Time range notification checking stopped');
        }
    }
    
    /**
     * 检查所有通知
     */
    checkNotifications() {
        if (!this.isEnabled) return;
        
        const now = new Date();
        
        this.notifications.forEach(notification => {
            if (!notification.enabled) return;
            
            try {
                if (this.shouldShowNotification(notification, now)) {
                    this.showNotification(notification);
                    this.markAsShown(notification, now);
                }
            } catch (error) {
                console.error('Error checking notification:', notification.id, error);
            }
        });
    }
    
    /**
     * 判断是否应该显示通知
     * @param {Object} notification 通知配置
     * @param {Date} now 当前时间
     * @returns {boolean} 是否应该显示
     */
    shouldShowNotification(notification, now) {
        // 检查是否在时间范围内
        if (!this.isInTimeRange(notification, now)) {
            return false;
        }
        
        // 检查是否已经显示过（基于时间范围和内容的组合判定）
        if (this.hasBeenShown(notification, now)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查当前时间是否在指定范围内
     * @param {Object} notification 通知配置
     * @param {Date} now 当前时间
     * @returns {boolean} 是否在时间范围内
     */
    isInTimeRange(notification, now) {
        const { type, timeRange } = notification;
        
        if (type === 'daily') {
            return this.isInDailyTimeRange(timeRange, now);
        } else if (type === 'absolute') {
            return this.isInAbsoluteTimeRange(timeRange, now);
        }
        
        return false;
    }
    
    /**
     * 检查是否在每日时间范围内
     * @param {Object} timeRange 时间范围配置
     * @param {Date} now 当前时间
     * @returns {boolean} 是否在范围内
     */
    isInDailyTimeRange(timeRange, now) {
        const { start, end } = timeRange;
        
        // 解析时间字符串 (HH:MM)
        const startTime = this.parseTimeString(start);
        const endTime = this.parseTimeString(end);
        
        if (!startTime || !endTime) {
            console.error('Invalid time format in daily range:', { start, end });
            return false;
        }
        
        const currentMinutes = now.getHours() * 60 + now.getMinutes();
        const startMinutes = startTime.hours * 60 + startTime.minutes;
        const endMinutes = endTime.hours * 60 + endTime.minutes;
        
        // 处理跨天的情况（如 22:00-02:00）
        if (startMinutes > endMinutes) {
            return currentMinutes >= startMinutes || currentMinutes < endMinutes;
        } else {
            return currentMinutes >= startMinutes && currentMinutes < endMinutes;
        }
    }
    
    /**
     * 检查是否在绝对时间范围内
     * @param {Object} timeRange 时间范围配置
     * @param {Date} now 当前时间
     * @returns {boolean} 是否在范围内
     */
    isInAbsoluteTimeRange(timeRange, now) {
        const { start, end } = timeRange;
        
        try {
            const startDate = new Date(start);
            const endDate = new Date(end);
            
            if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                console.error('Invalid absolute time format:', { start, end });
                return false;
            }
            
            return now >= startDate && now < endDate;
        } catch (error) {
            console.error('Error parsing absolute time range:', error);
            return false;
        }
    }
    
    /**
     * 解析时间字符串 (HH:MM)
     * @param {string} timeStr 时间字符串
     * @returns {Object|null} 解析结果 {hours, minutes}
     */
    parseTimeString(timeStr) {
        const match = timeStr.match(/^(\d{1,2}):(\d{2})$/);
        if (!match) return null;
        
        const hours = parseInt(match[1], 10);
        const minutes = parseInt(match[2], 10);
        
        if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
            return null;
        }
        
        return { hours, minutes };
    }
    
    /**
     * 检查通知是否已经显示过
     * @param {Object} notification 通知配置
     * @param {Date} now 当前时间
     * @returns {boolean} 是否已显示过
     */
    hasBeenShown(notification, now) {
        const key = this.getStorageKey(notification, now);
        const lastShown = localStorage.getItem(key);
        
        if (!lastShown) return false;
        
        try {
            const lastShownDate = new Date(lastShown);
            
            if (notification.type === 'daily') {
                // 每日提示：检查是否在同一天显示过
                const today = this.getDateString(now);
                const lastShownDay = this.getDateString(lastShownDate);
                return today === lastShownDay;
            } else if (notification.type === 'absolute') {
                // 绝对时间提示：在整个时间范围内只显示一次
                return true;
            }
        } catch (error) {
            console.error('Error checking if notification was shown:', error);
        }
        
        return false;
    }
    
    /**
     * 标记通知为已显示
     * @param {Object} notification 通知配置
     * @param {Date} now 当前时间
     */
    markAsShown(notification, now) {
        const key = this.getStorageKey(notification, now);
        localStorage.setItem(key, now.toISOString());
    }
    
    /**
     * 获取存储键
     * @param {Object} notification 通知配置
     * @param {Date} now 当前时间
     * @returns {string} 存储键
     */
    getStorageKey(notification, now) {
        // 使用通知ID、内容哈希和时间范围生成唯一键
        const contentHash = this.hashContent(notification.content);
        const timeRangeHash = this.hashContent(notification.timeRange);
        
        if (notification.type === 'daily') {
            const dateStr = this.getDateString(now);
            return `${this.storagePrefix}${notification.id}-${contentHash}-${timeRangeHash}-${dateStr}`;
        } else {
            return `${this.storagePrefix}${notification.id}-${contentHash}-${timeRangeHash}`;
        }
    }
    
    /**
     * 生成内容哈希
     * @param {Object} content 内容对象
     * @returns {string} 哈希值
     */
    hashContent(content) {
        const str = JSON.stringify(content);
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(36);
    }
    
    /**
     * 获取日期字符串 (YYYY-MM-DD)
     * @param {Date} date 日期对象
     * @returns {string} 日期字符串
     */
    getDateString(date) {
        return date.getFullYear() + '-' + 
               String(date.getMonth() + 1).padStart(2, '0') + '-' + 
               String(date.getDate()).padStart(2, '0');
    }
    
    /**
     * 显示通知
     * @param {Object} notification 通知配置
     */
    showNotification(notification) {
        const { content } = notification;
        
        console.log('Showing time range notification:', notification.id);
        
        // 使用现有的showToast函数显示通知
        if (typeof showToast === 'function') {
            const message = content.title ? `${content.title}: ${content.message}` : content.message;
            showToast(message, content.type || 'info', 5000);
        } else {
            // 降级处理：创建简单的通知元素
            this.showFallbackNotification(content);
        }
    }
    
    /**
     * 显示降级通知（当showToast不可用时）
     * @param {Object} content 通知内容
     */
    showFallbackNotification(content) {
        const notification = document.createElement('div');
        notification.className = 'time-range-notification';
        notification.innerHTML = `
            <div class="time-range-notification-content">
                ${content.title ? `<div class="notification-title">${content.title}</div>` : ''}
                <div class="notification-message">${content.message}</div>
            </div>
        `;
        
        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--card-background, #fff);
            border: 1px solid var(--border-color, #ddd);
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 10000;
            max-width: 300px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // 自动移除
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }
    
    /**
     * 更新配置
     * @param {Object} newConfig 新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.isEnabled = this.config.enabled !== false;
        this.notifications = this.config.notifications || [];
        
        if (this.isEnabled) {
            this.startChecking();
        } else {
            this.stopChecking();
        }
        
        console.log('Time range notification config updated', this.config);
    }
    
    /**
     * 添加新的通知配置
     * @param {Object} notification 通知配置
     */
    addNotification(notification) {
        if (!notification.id) {
            notification.id = 'notification-' + Date.now();
        }
        
        this.notifications.push(notification);
        console.log('Added new time range notification:', notification.id);
    }
    
    /**
     * 移除通知配置
     * @param {string} notificationId 通知ID
     */
    removeNotification(notificationId) {
        const index = this.notifications.findIndex(n => n.id === notificationId);
        if (index !== -1) {
            this.notifications.splice(index, 1);
            console.log('Removed time range notification:', notificationId);
        }
    }
    
    /**
     * 清除所有显示记录
     */
    clearAllRecords() {
        const keys = Object.keys(localStorage).filter(key => 
            key.startsWith(this.storagePrefix)
        );
        
        keys.forEach(key => localStorage.removeItem(key));
        console.log('Cleared all time range notification records:', keys.length);
    }
    
    /**
     * 获取状态信息
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            enabled: this.isEnabled,
            notificationCount: this.notifications.length,
            isChecking: !!this.checkInterval,
            checkInterval: this.checkIntervalMs,
            configLoaded: this.configLoaded
        };
    }

    /**
     * 获取所有通知配置
     * @returns {Array} 通知配置列表
     */
    getAllNotifications() {
        return [...this.notifications];
    }

    /**
     * 根据ID获取通知配置
     * @param {string} notificationId 通知ID
     * @returns {Object|null} 通知配置
     */
    getNotificationById(notificationId) {
        return this.notifications.find(n => n.id === notificationId) || null;
    }

    /**
     * 获取启用的通知配置
     * @returns {Array} 启用的通知配置列表
     */
    getEnabledNotifications() {
        return this.notifications.filter(n => n.enabled);
    }

    /**
     * 从配置文件加载预设配置
     * @param {string} presetName 预设名称
     */
    async loadPreset(presetName) {
        if (!this.configLoaded) {
            console.warn('Config not loaded yet, cannot load preset');
            return false;
        }

        try {
            // 重新加载配置文件以获取最新的预设
            const configResponse = await fetch('data/time-notifications.json');
            if (!configResponse.ok) {
                throw new Error(`Failed to load config: ${configResponse.status}`);
            }

            const timeConfig = await configResponse.json();
            const preset = timeConfig.presets?.[presetName];

            if (!preset) {
                console.error(`Preset "${presetName}" not found`);
                return false;
            }

            // 清除现有通知
            this.notifications = [];

            // 添加预设通知
            preset.notifications.forEach(notification => {
                this.notifications.push({
                    ...notification,
                    id: notification.id || `preset-${presetName}-${Date.now()}`
                });
            });

            console.log(`Loaded preset "${presetName}" with ${this.notifications.length} notifications`);
            return true;

        } catch (error) {
            console.error('Error loading preset:', error);
            return false;
        }
    }

    /**
     * 获取可用的预设列表
     * @returns {Array} 预设列表
     */
    async getAvailablePresets() {
        try {
            const configResponse = await fetch('data/time-notifications.json');
            if (!configResponse.ok) {
                throw new Error(`Failed to load config: ${configResponse.status}`);
            }

            const timeConfig = await configResponse.json();
            const presets = timeConfig.presets || {};

            return Object.keys(presets).map(key => ({
                id: key,
                name: presets[key].name,
                description: presets[key].description,
                notificationCount: presets[key].notifications?.length || 0
            }));

        } catch (error) {
            console.error('Error getting available presets:', error);
            return [];
        }
    }
    
    /**
     * 销毁管理器
     */
    destroy() {
        this.stopChecking();
        console.log('TimeRangeNotificationManager destroyed');
    }
}

// 导出到全局
window.TimeRangeNotificationManager = TimeRangeNotificationManager;
