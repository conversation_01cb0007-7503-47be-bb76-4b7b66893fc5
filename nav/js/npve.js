const { createApp } = Vue;

// 树节点组件
const TreeNode = {
    name: 'TreeNode',
    props: {
        node: Object,
        level: Number,
        selectedId: String
    },
    emits: ['select', 'toggle'],
    data() {
        return {
            isExpanded: true
        };
    },
    computed: {
        hasChildren() {
            return this.node.children && this.node.children.length > 0;
        },
        hasSites() {
            return this.node.sites && this.node.sites.length > 0;
        },
        isSelected() {
            return this.selectedId === this.node.id;
        },
        nodeType() {
            return this.hasChildren || this.hasSites ? 'category' : 'site';
        },
        paddingLeft() {
            return `${this.level * 20}px`;
        }
    },
    methods: {
        selectNode() {
            this.$emit('select', {
                id: this.node.id,
                type: this.nodeType,
                data: this.node
            });
        },
        toggleExpanded() {
            this.isExpanded = !this.isExpanded;
            this.$emit('toggle', this.node.id, this.isExpanded);
        }
    },
    template: `
        <div class="tree-node">
            <div 
                class="tree-node-content"
                :class="{ selected: isSelected, [nodeType]: true }"
                :style="{ paddingLeft: paddingLeft }"
                @click="selectNode"
            >
                <span 
                    v-if="hasChildren || hasSites"
                    class="tree-node-toggle"
                    @click.stop="toggleExpanded"
                >
                    {{ isExpanded ? '▼' : '▶' }}
                </span>
                <span v-else class="tree-node-toggle"></span>
                <span class="tree-node-icon">{{ node.icon || '📁' }}</span>
                <span class="tree-node-name">{{ node.name }}</span>
            </div>
            
            <div v-if="isExpanded" class="tree-node-children">
                <tree-node 
                    v-for="child in node.children" 
                    :key="child.id"
                    :node="child"
                    :level="level + 1"
                    :selected-id="selectedId"
                    @select="$emit('select', $event)"
                    @toggle="$emit('toggle', $event)"
                ></tree-node>
                
                <tree-node 
                    v-for="site in node.sites" 
                    :key="site.id"
                    :node="site"
                    :level="level + 1"
                    :selected-id="selectedId"
                    @select="$emit('select', $event)"
                    @toggle="$emit('toggle', $event)"
                ></tree-node>
            </div>
        </div>
    `
};

// 主应用
const app = createApp({
    components: {
        TreeNode
    },
    data() {
        return {
            // 数据状态
            jsonData: null,
            selectedNode: null,
            selectedNodeId: null,
            searchQuery: '',

            // UI状态
            showRecoveryPrompt: false,
            showHelpDialog: false,
            messages: [],

            // 本地存储键名
            STORAGE_KEY: 'nav_admin_data',
            RECOVERY_KEY: 'nav_admin_recovery'
        };
    },
    computed: {
        hasData() {
            return this.jsonData && this.jsonData.categories && this.jsonData.categories.length > 0;
        },
        
        filteredCategories() {
            if (!this.hasData) return [];
            
            if (!this.searchQuery.trim()) {
                return this.jsonData.categories;
            }
            
            const query = this.searchQuery.toLowerCase();
            return this.filterNodes(this.jsonData.categories, query);
        },
        
        tagsString: {
            get() {
                if (this.selectedNode && this.selectedNode.data.tags) {
                    return this.selectedNode.data.tags.join(', ');
                }
                return '';
            },
            set(value) {
                if (this.selectedNode && this.selectedNode.data) {
                    this.selectedNode.data.tags = value.split(',').map(tag => tag.trim()).filter(tag => tag);
                }
            }
        },

        hasChildrenAndSites() {
            if (!this.selectedNode || this.selectedNode.type !== 'category') return false;

            const hasChildren = this.selectedNode.data.children && this.selectedNode.data.children.length > 0;
            const hasSites = this.selectedNode.data.sites && this.selectedNode.data.sites.length > 0;

            return hasChildren && hasSites;
        }
    },
    methods: {
        // 文件操作
        triggerFileInput() {
            document.getElementById('fileInput').click();
        },
        
        loadFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    this.validateJsonStructure(data);
                    this.jsonData = data;
                    this.selectedNode = null;
                    this.selectedNodeId = null;
                    this.saveToStorage();
                    this.showMessage('文件加载成功！', 'success');
                } catch (error) {
                    this.showMessage(`JSON文件解析失败！请检查文件格式。\n错误信息: ${error.message}`, 'error');
                }
                // 重置文件输入，允许重新选择同一文件
                event.target.value = '';
            };
            reader.onerror = () => {
                this.showMessage('文件读取失败', 'error');
            };
            reader.readAsText(file);
        },
        
        validateJsonStructure(data) {
            if (!data || typeof data !== 'object') {
                throw new Error('JSON格式无效');
            }
            if (!data.categories || !Array.isArray(data.categories)) {
                throw new Error('缺少categories数组');
            }
            // 可以添加更多验证逻辑
        },
        
        exportData() {
            if (!this.hasData) {
                this.showMessage('没有数据可导出', 'warning');
                return;
            }

            try {
                // 验证数据完整性
                this.validateExportData();

                const jsonString = JSON.stringify(this.jsonData, null, 2);
                const blob = new Blob([jsonString], { type: 'application/json' });
                const url = URL.createObjectURL(blob);

                // 生成带时间戳的文件名
                const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                const filename = `firstshare_${timestamp}.json`;

                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                // 导出成功后清理本地存储
                this.clearStorage();
                this.showMessage(`文件导出成功！文件名: ${filename}`, 'success');
            } catch (error) {
                this.showMessage(`导出失败: ${error.message}`, 'error');
            }
        },

        validateExportData() {
            if (!this.jsonData || !this.jsonData.categories) {
                throw new Error('数据结构无效');
            }

            // 验证所有节点都有必需的字段
            const validateNode = (node, path = '') => {
                if (!node.id || !node.name) {
                    throw new Error(`节点缺少必需字段 (路径: ${path})`);
                }

                if (node.children) {
                    node.children.forEach((child, index) => {
                        validateNode(child, `${path}/children[${index}]`);
                    });
                }

                if (node.sites) {
                    node.sites.forEach((site, index) => {
                        if (!site.url) {
                            throw new Error(`网站缺少URL (路径: ${path}/sites[${index}])`);
                        }
                        validateNode(site, `${path}/sites[${index}]`);
                    });
                }
            };

            this.jsonData.categories.forEach((category, index) => {
                validateNode(category, `categories[${index}]`);
            });
        },
        
        clearData() {
            if (confirm('确定要清空所有数据吗？此操作不可撤销。')) {
                this.jsonData = null;
                this.selectedNode = null;
                this.selectedNodeId = null;
                this.clearStorage();
                this.showMessage('数据已清空', 'info');
            }
        },
        
        // 节点操作
        selectNode(nodeInfo) {
            this.selectedNode = nodeInfo;
            this.selectedNodeId = nodeInfo.id;
        },

        addRootCategory() {
            if (!this.jsonData) {
                this.jsonData = { categories: [] };
            }

            const newCategory = {
                id: this.generateUniqueId(),
                name: '新分类',
                icon: '📁',
                children: []
            };

            this.jsonData.categories.push(newCategory);
            this.saveToStorage();
            this.showMessage('已添加新的根分类', 'success');

            // 自动选中新创建的分类
            this.selectNode({
                id: newCategory.id,
                type: 'category',
                data: newCategory
            });
        },

        addChildCategory() {
            if (!this.selectedNode || this.selectedNode.type !== 'category') {
                this.showMessage('请先选择一个分类', 'error');
                return;
            }

            // 检查是否已有网站，如果有则不能添加子分类
            if (this.selectedNode.data.sites && this.selectedNode.data.sites.length > 0) {
                this.showMessage('已包含网站的分类不能添加子分类', 'error');
                return;
            }

            const newCategory = {
                id: this.generateUniqueId(),
                name: '新子分类',
                icon: '📁',
                sites: []
            };

            if (!this.selectedNode.data.children) {
                this.selectedNode.data.children = [];
            }

            this.selectedNode.data.children.push(newCategory);
            this.saveToStorage();
            this.showMessage('已添加新的子分类', 'success');
        },

        addSite() {
            if (!this.selectedNode || this.selectedNode.type !== 'category') {
                this.showMessage('请先选择一个分类', 'error');
                return;
            }

            // 检查是否已有子分类，如果有则不能添加网站
            if (this.selectedNode.data.children && this.selectedNode.data.children.length > 0) {
                this.showMessage('已包含子分类的分类不能添加网站', 'error');
                return;
            }

            const newSite = {
                id: this.generateUniqueId(),
                name: '新网站',
                description: '请填写网站描述',
                icon: '🌐',
                url: 'https://example.com',
                tags: []
            };

            if (!this.selectedNode.data.sites) {
                this.selectedNode.data.sites = [];
            }

            this.selectedNode.data.sites.push(newSite);
            this.saveToStorage();
            this.showMessage('已添加新网站', 'success');

            // 自动选中新创建的网站
            this.selectNode({
                id: newSite.id,
                type: 'site',
                data: newSite
            });
        },
        
        toggleNode(nodeId, isExpanded) {
            // 处理节点展开/折叠状态
            // 这里可以保存展开状态到localStorage
        },
        
        saveChanges() {
            if (!this.selectedNode) return;

            try {
                // 验证必填字段
                if (!this.selectedNode.data.name || !this.selectedNode.data.name.trim()) {
                    this.showMessage('名称不能为空', 'error');
                    return;
                }

                if (this.selectedNode.type === 'site') {
                    if (!this.selectedNode.data.url || !this.selectedNode.data.url.trim()) {
                        this.showMessage('URL不能为空', 'error');
                        return;
                    }

                    // 验证URL格式
                    try {
                        new URL(this.selectedNode.data.url);
                    } catch (e) {
                        this.showMessage('URL格式不正确', 'error');
                        return;
                    }
                }

                // 数据已经通过v-model自动更新到原始数据
                this.saveToStorage();
                this.showMessage('修改已保存', 'success');
            } catch (error) {
                this.showMessage(`保存失败: ${error.message}`, 'error');
            }
        },
        
        deleteNode() {
            if (!this.selectedNode) return;

            const nodeName = this.selectedNode.data.name;
            const nodeType = this.selectedNode.type === 'category' ? '分类' : '网站';

            // 检查分类是否有子项
            if (this.selectedNode.type === 'category') {
                const hasChildren = this.selectedNode.data.children && this.selectedNode.data.children.length > 0;
                const hasSites = this.selectedNode.data.sites && this.selectedNode.data.sites.length > 0;

                if (hasChildren || hasSites) {
                    this.showMessage('不能删除包含子项的分类，请先删除所有子项', 'error');
                    return;
                }
            }

            if (confirm(`确定要删除${nodeType} "${nodeName}" 吗？此操作不可撤销。`)) {
                try {
                    const removed = this.removeNodeFromData(this.selectedNode.data.id);
                    if (removed) {
                        this.selectedNode = null;
                        this.selectedNodeId = null;
                        this.saveToStorage();
                        this.showMessage('删除成功', 'success');
                    } else {
                        this.showMessage('删除失败：未找到指定项目', 'error');
                    }
                } catch (error) {
                    this.showMessage(`删除失败: ${error.message}`, 'error');
                }
            }
        },
        
        // 搜索过滤
        filterNodes(nodes, query) {
            const filtered = [];

            for (const node of nodes) {
                const matchesName = node.name.toLowerCase().includes(query);
                const matchesDescription = node.description && node.description.toLowerCase().includes(query);
                const matchesTags = node.tags && node.tags.some(tag => tag.toLowerCase().includes(query));
                const matchesUrl = node.url && node.url.toLowerCase().includes(query);

                const nodeMatches = matchesName || matchesDescription || matchesTags || matchesUrl;

                if (nodeMatches) {
                    // 如果当前节点匹配，包含整个节点（包括所有子项）
                    filtered.push(node);
                } else {
                    // 如果当前节点不匹配，检查子项
                    let hasMatchingChildren = false;
                    const nodeClone = { ...node };

                    if (node.children) {
                        const filteredChildren = this.filterNodes(node.children, query);
                        if (filteredChildren.length > 0) {
                            nodeClone.children = filteredChildren;
                            hasMatchingChildren = true;
                        }
                    }

                    if (node.sites) {
                        const filteredSites = this.filterNodes(node.sites, query);
                        if (filteredSites.length > 0) {
                            nodeClone.sites = filteredSites;
                            hasMatchingChildren = true;
                        }
                    }

                    if (hasMatchingChildren) {
                        filtered.push(nodeClone);
                    }
                }
            }

            return filtered;
        },
        
        // 本地存储
        saveToStorage() {
            if (this.hasData) {
                localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.jsonData));
            }
        },
        
        loadFromStorage() {
            const stored = localStorage.getItem(this.STORAGE_KEY);
            if (stored) {
                try {
                    this.jsonData = JSON.parse(stored);
                    return true;
                } catch (error) {
                    console.error('Failed to load from storage:', error);
                    localStorage.removeItem(this.STORAGE_KEY);
                }
            }
            return false;
        },
        
        clearStorage() {
            localStorage.removeItem(this.STORAGE_KEY);
            localStorage.removeItem(this.RECOVERY_KEY);
        },
        
        // 数据恢复
        checkRecovery() {
            const hasStoredData = localStorage.getItem(this.STORAGE_KEY);
            if (hasStoredData && !this.hasData) {
                this.showRecoveryPrompt = true;
            }
        },
        
        recoverData() {
            if (this.loadFromStorage()) {
                this.showRecoveryPrompt = false;
                this.showMessage('数据恢复成功！', 'success');
            } else {
                this.showMessage('数据恢复失败', 'error');
            }
        },
        
        dismissRecovery() {
            this.showRecoveryPrompt = false;
            this.clearStorage();
        },
        
        // 工具方法
        generateUniqueId() {
            return 'id_' + Date.now().toString(36) + Math.random().toString(36).substr(2, 9);
        },

        removeNodeFromData(nodeId) {
            // 递归删除节点的实现
            const removeFromArray = (arr) => {
                for (let i = arr.length - 1; i >= 0; i--) {
                    if (arr[i].id === nodeId) {
                        arr.splice(i, 1);
                        return true;
                    }
                    if (arr[i].children && removeFromArray(arr[i].children)) {
                        return true;
                    }
                    if (arr[i].sites && removeFromArray(arr[i].sites)) {
                        return true;
                    }
                }
                return false;
            };

            if (this.jsonData && this.jsonData.categories) {
                return removeFromArray(this.jsonData.categories);
            }
            return false;
        },
        
        showMessage(text, type = 'info') {
            const message = {
                id: this.generateUniqueId(),
                text,
                type
            };

            this.messages.push(message);

            // 3秒后自动移除消息
            setTimeout(() => {
                this.removeMessage(message.id);
            }, 3000);
        },

        removeMessage(messageId) {
            const index = this.messages.findIndex(msg => msg.id === messageId);
            if (index > -1) {
                this.messages.splice(index, 1);
            }
        },

        // 键盘快捷键
        setupKeyboardShortcuts() {
            this.keyboardHandler = (event) => {
                // Ctrl/Cmd + S: 保存
                if ((event.ctrlKey || event.metaKey) && event.key === 's') {
                    event.preventDefault();
                    if (this.selectedNode) {
                        this.saveChanges();
                    }
                }

                // Ctrl/Cmd + E: 导出
                if ((event.ctrlKey || event.metaKey) && event.key === 'e') {
                    event.preventDefault();
                    this.exportData();
                }

                // Delete: 删除选中项
                if (event.key === 'Delete' && this.selectedNode) {
                    event.preventDefault();
                    this.deleteNode();
                }

                // Escape: 取消选择
                if (event.key === 'Escape') {
                    this.selectedNode = null;
                    this.selectedNodeId = null;
                }
            };

            document.addEventListener('keydown', this.keyboardHandler);
        },

        removeKeyboardShortcuts() {
            if (this.keyboardHandler) {
                document.removeEventListener('keydown', this.keyboardHandler);
            }
        },

        // 帮助对话框
        showHelp() {
            this.showHelpDialog = true;
        },

        closeHelp() {
            this.showHelpDialog = false;
        }
    },
    
    mounted() {
        this.checkRecovery();
        this.setupKeyboardShortcuts();
    },

    beforeUnmount() {
        this.removeKeyboardShortcuts();
    },
    
    watch: {
        jsonData: {
            handler() {
                if (this.hasData) {
                    this.saveToStorage();
                }
            },
            deep: true
        }
    }
});

app.mount('#app');
