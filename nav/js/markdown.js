/**
 * Markdown 管理器
 * 负责处理 Markdown 文件的加载、渲染和显示
 */
class MarkdownManager {
    constructor() {
        this.modal = null;
        this.modalTitle = null;
        this.modalContent = null;
        this.modalClose = null;
        this.modalBackdrop = null;
        this.isOpen = false;
        
        this.init();
    }
    
    /**
     * 初始化 Markdown 管理器
     */
    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupElements();
                this.bindEvents();
                this.configureMarked();
            });
        } else {
            this.setupElements();
            this.bindEvents();
            this.configureMarked();
        }
    }
    
    /**
     * 设置DOM元素
     */
    setupElements() {
        this.modal = document.getElementById('markdownModal');
        this.modalTitle = document.getElementById('markdownModalTitle');
        this.modalContent = document.getElementById('markdownContent');
        this.modalClose = document.getElementById('markdownModalClose');
        this.modalBackdrop = this.modal?.querySelector('.markdown-modal-backdrop');
        
        if (!this.modal || !this.modalContent) {
            console.error('Markdown modal elements not found');
            return;
        }
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 关闭按钮点击
        if (this.modalClose) {
            this.modalClose.addEventListener('click', () => {
                this.closeModal();
            });
        }
        
        // 背景点击关闭
        if (this.modalBackdrop) {
            this.modalBackdrop.addEventListener('click', () => {
                this.closeModal();
            });
        }
        
        // ESC 键和空格键关闭 - 使用capture阶段确保优先处理
        document.addEventListener('keydown', (e) => {
            if (this.isOpen) {
                if (e.key === 'Escape') {
                    e.preventDefault();
                    e.stopPropagation(); // 阻止事件传播
                    this.closeModal();
                } else if (e.key === ' ') {
                    // 检查是否应该处理空格键（避免与输入框等冲突）
                    if (this.shouldHandleSpaceKey()) {
                        e.preventDefault();
                        e.stopPropagation(); // 阻止事件传播到其他监听器
                        this.closeModal();
                    }
                }
            }
        }, true); // 使用capture阶段
    }
    
    /**
     * 配置 Marked.js 渲染器
     */
    configureMarked() {
        if (typeof marked !== 'undefined') {
            // 配置 marked 选项
            marked.setOptions({
                breaks: true,          // 支持换行
                gfm: true,            // GitHub 风格的 Markdown
                tables: true,         // 支持表格
                sanitize: false,      // 不过滤 HTML（注意安全性）
                smartypants: true,    // 智能标点符号
            });
            
            // 自定义渲染器
            const renderer = new marked.Renderer();
            
            // 自定义代码块渲染
            renderer.code = (code, language) => {
                const validLanguage = language && language.trim() ? language.trim() : 'text';
                return `<pre><code class="language-${validLanguage}">${code}</code></pre>`;
            };
            
            // 自定义链接渲染（在新窗口打开）
            renderer.link = (href, title, text) => {
                const titleAttr = title ? ` title="${title}"` : '';
                return `<a href="${href}"${titleAttr} target="_blank" rel="noopener noreferrer">${text}</a>`;
            };
            
            marked.setOptions({ renderer });
            
            console.log('Marked.js configured successfully');
        } else {
            console.error('Marked.js not loaded');
        }
    }
    
    /**
     * 加载并显示 Markdown 文件
     * @param {string} filePath Markdown 文件路径
     * @param {string} title 模态框标题
     */
    async loadAndShow(filePath, title = '文档') {
        try {
            this.showLoadingState();
            
            console.log(`Loading markdown file: ${filePath}`);
            
            // 加载 Markdown 文件
            const response = await fetch(filePath);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const markdownText = await response.text();
            
            // 渲染 Markdown
            const htmlContent = this.renderMarkdown(markdownText);
            
            // 显示内容
            this.showContent(htmlContent, title);
            
            console.log('Markdown loaded and rendered successfully');
            
        } catch (error) {
            console.error('Failed to load markdown:', error);
            this.showErrorState(error.message);
        }
    }
    
    /**
     * 渲染 Markdown 文本
     * @param {string} markdownText Markdown 文本
     * @returns {string} 渲染后的 HTML
     */
    renderMarkdown(markdownText) {
        if (typeof marked === 'undefined') {
            throw new Error('Marked.js not available');
        }
        
        try {
            return marked.parse(markdownText);
        } catch (error) {
            console.error('Markdown parsing error:', error);
            return `<p class="text-danger">Markdown 解析失败: ${error.message}</p>`;
        }
    }
    
    /**
     * 显示加载状态
     */
    showLoadingState() {
        if (this.modalTitle) {
            this.modalTitle.textContent = '加载中...';
        }
        
        if (this.modalContent) {
            this.modalContent.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-3 text-muted">正在加载文档...</p>
                </div>
            `;
        }
        
        this.openModal();
    }
    
    /**
     * 显示内容
     * @param {string} htmlContent 渲染后的 HTML 内容
     * @param {string} title 标题
     */
    showContent(htmlContent, title) {
        if (this.modalTitle) {
            this.modalTitle.textContent = title;
        }
        
        if (this.modalContent) {
            this.modalContent.innerHTML = htmlContent;
        }
        
        // 确保模态框是打开的
        if (!this.isOpen) {
            this.openModal();
        }
    }
    
    /**
     * 显示错误状态
     * @param {string} errorMessage 错误信息
     */
    showErrorState(errorMessage) {
        if (this.modalTitle) {
            this.modalTitle.textContent = '加载失败';
        }
        
        if (this.modalContent) {
            this.modalContent.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h4>文档加载失败</h4>
                    <p class="text-muted">${errorMessage}</p>
                    <button class="btn btn-primary mt-3" onclick="this.closest('.markdown-modal').style.display='none'">
                        关闭
                    </button>
                </div>
            `;
        }
        
        // 确保模态框是打开的
        if (!this.isOpen) {
            this.openModal();
        }
    }
    
    /**
     * 打开模态框
     */
    openModal() {
        if (this.modal) {
            this.modal.style.display = 'flex';
            this.isOpen = true;
            
            // 阻止背景滚动
            document.body.style.overflow = 'hidden';
            
            // 添加打开动画
            setTimeout(() => {
                this.modal.classList.add('show');
            }, 10);
            
            console.log('Markdown modal opened');
        }
    }
    
    /**
     * 关闭模态框
     */
    closeModal() {
        if (this.modal) {
            this.modal.classList.remove('show');
            
            setTimeout(() => {
                this.modal.style.display = 'none';
                this.isOpen = false;
                
                // 恢复背景滚动
                document.body.style.overflow = '';
            }, 300);
            
            console.log('Markdown modal closed');
        }
    }
    
    /**
     * 检查是否打开
     * @returns {boolean} 是否打开
     */
    isModalOpen() {
        return this.isOpen;
    }

    /**
     * 判断是否应该处理空格键
     * @returns {boolean} 是否应该处理
     */
    shouldHandleSpaceKey() {
        // 如果有元素聚焦且是输入类元素，不处理空格键
        const activeElement = document.activeElement;
        if (activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.contentEditable === 'true'
        )) {
            return false;
        }

        // 如果模态框内容区域可滚动且用户正在滚动，不处理空格键
        if (this.modalContent) {
            const isScrollable = this.modalContent.scrollHeight > this.modalContent.clientHeight;
            if (isScrollable) {
                // 检查是否有滚动条且用户可能在使用空格键滚动
                // 这里我们允许空格键关闭，因为用户体验优化要求
                // 如果需要滚动，用户可以使用鼠标滚轮或方向键
            }
        }

        return true;
    }
    
    /**
     * 获取支持的文件类型
     * @returns {Array} 支持的文件扩展名
     */
    getSupportedExtensions() {
        return ['.md', '.markdown', '.txt'];
    }
    
    /**
     * 检查文件是否为 Markdown 文件
     * @param {string} filePath 文件路径
     * @returns {boolean} 是否为 Markdown 文件
     */
    isMarkdownFile(filePath) {
        const supportedExtensions = this.getSupportedExtensions();
        return supportedExtensions.some(ext => filePath.toLowerCase().endsWith(ext));
    }
}

// 导出 Markdown 管理器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MarkdownManager;
} 