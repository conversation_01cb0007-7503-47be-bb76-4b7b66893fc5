/**
 * 平台检测和适配模块
 * 用于检测用户操作系统并适配相应的快捷键显示
 */

// 检测当前操作系统
const Platform = {
    isMac: /Mac|iPod|iPhone|iPad/.test(navigator.platform),
    isWindows: /Win/.test(navigator.platform),
    isLinux: /Linux/.test(navigator.platform),
    
    // 获取适合当前平台的修饰键名称
    getModifierKey() {
        return this.isMac ? '⌘' : 'Ctrl';
    },
    
    // 获取适合当前平台的键盘组合显示文本
    getShortcutText(key) {
        const modifier = this.getModifierKey();
        
        // 处理包含Shift的组合键
        if (key.toLowerCase().includes('shift+')) {
            const actualKey = key.replace(/shift\+/i, '');
            return `${modifier}+Shift+${actualKey}`;
        }
        
        return `${modifier}+${key}`;
    },
    
    // 检查快捷键事件是否匹配
    isShortcut(event, key) {
        const modifierPressed = this.isMac ? event.metaKey : event.ctrlKey;
        return modifierPressed && event.key.toLowerCase() === key.toLowerCase();
    },
    
    // 更新页面中的快捷键显示文本
    updateShortcutDisplays() {
        // 更新搜索框快捷键提示
        const searchInput = document.getElementById('searchInput');
        const searchShortcut = document.getElementById('searchShortcut');
        
        if (searchInput) {
            // 更新占位符文本，提示空格键功能
            searchInput.placeholder = `搜索网站名称、描述、标签... (空格键唤起搜索)`;
        }
        
        if (searchShortcut) {
            searchShortcut.textContent = this.getShortcutText('K');
        }
        
        console.log(`平台检测: ${this.getPlatformName()}, 快捷键前缀: ${this.getModifierKey()}`);
    },
    
    // 获取平台名称
    getPlatformName() {
        if (this.isMac) return 'macOS';
        if (this.isWindows) return 'Windows';
        if (this.isLinux) return 'Linux';
        return 'Unknown';
    },
    
    // 获取所有快捷键列表（用于帮助显示）
    getShortcutList() {
        const modifier = this.getModifierKey();
        return [
            { keys: 'Space', description: '唤起/关闭搜索栏' },
            { keys: `${modifier}+K`, description: '清除搜索栏内容' },
            { keys: `${modifier}+Shift+K`, description: '快速清除所有筛选条件' },
            { keys: 'ESC', description: '清空搜索/关闭搜索栏/关闭筛选器' },
            { keys: `${modifier}+ESC`, description: '重置主题配置' },
            { keys: '↑/↓', description: '搜索结果导航' },
            { keys: '←/→', description: '标签筛选器导航' },
            { keys: 'Enter', description: '选择搜索结果/激活标签' },
            { keys: 'Home/End', description: '跳转到首/尾标签' },
            { keys: '?', description: '显示帮助信息' }
        ];
    }
};

// 页面加载完成后更新快捷键显示
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        Platform.updateShortcutDisplays();
    });
}

// 导出平台检测对象
if (typeof window !== 'undefined') {
    window.Platform = Platform;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = Platform;
} 