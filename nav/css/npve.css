/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* 顶部工具栏 */
.header {
    background: #fff;
    border-bottom: 1px solid #e0e0e0;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
}

.toolbar {
    display: flex;
    gap: 0.5rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #2980b9;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #229954;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background: #e67e22;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c0392b;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #7f8c8d;
}

/* 数据恢复提示 */
.recovery-prompt {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 1rem 2rem;
    margin: 0;
}

.alert {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alert p {
    margin: 0;
    color: #856404;
}

.alert .btn {
    margin-left: 0.5rem;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    height: calc(100vh - 80px);
    background: #fff;
}

/* 左侧边栏 */
.sidebar {
    width: 350px;
    border-right: 1px solid #e0e0e0;
    background: #fafafa;
    display: flex;
    flex-direction: column;
}

.search-box {
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.search-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.search-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.tree-container {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem 0;
}

.empty-state {
    padding: 2rem;
    text-align: center;
    color: #7f8c8d;
}

/* 树节点样式 */
.tree-node {
    user-select: none;
}

.tree-node-content {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-left: 3px solid transparent;
}

.tree-node-content:hover {
    background-color: #ecf0f1;
}

.tree-node-content.selected {
    background-color: #e3f2fd;
    border-left-color: #3498db;
}

.tree-node-content.category {
    font-weight: 600;
}

.tree-node-content.site {
    font-weight: normal;
    color: #555;
}

.tree-node-icon {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.tree-node-name {
    flex: 1;
}

.tree-node-toggle {
    margin-right: 0.5rem;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: #7f8c8d;
}

.tree-node-children {
    margin-left: 1rem;
}

/* 右侧编辑面板 */
.editor-panel {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.empty-editor {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #7f8c8d;
    font-size: 1.1rem;
}

.editor-form h3 {
    margin-bottom: 1.5rem;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-control.readonly {
    background-color: #f8f9fa;
    color: #6c757d;
}

textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
}

/* 模态对话框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    margin: 2rem;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #7f8c8d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #2c3e50;
}

.modal-body {
    padding: 1.5rem;
}

.modal-body h4 {
    color: #2c3e50;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
}

.modal-body h4:first-child {
    margin-top: 0;
}

.modal-body ul, .modal-body ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.modal-body li {
    margin-bottom: 0.5rem;
}

kbd {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    padding: 0.2rem 0.4rem;
    font-size: 0.85rem;
    font-family: monospace;
    color: #495057;
}

/* 消息提示 */
.message-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 400px;
}

.message {
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    animation: slideIn 0.3s ease;
    position: relative;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.message.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.message-close {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
}

.message-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .main-content {
        flex-direction: column;
        height: auto;
    }

    .sidebar {
        width: 100%;
        max-height: 300px;
    }

    .editor-panel {
        padding: 1rem;
    }

    .message-container {
        left: 20px;
        right: 20px;
        max-width: none;
    }
}
