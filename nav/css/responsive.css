/* 响应式显示工具类 */
.d-none {
    display: none !important;
}

.d-inline {
    display: inline !important;
}

.d-block {
    display: block !important;
}

/* 大屏幕显示类 */
@media (min-width: 992px) {
    .d-lg-inline {
        display: inline !important;
    }

    .d-lg-block {
        display: block !important;
    }

    .d-lg-none {
        display: none !important;
    }
}

/* 中等屏幕显示类 */
@media (max-width: 767px) {
    .d-md-none {
        display: none !important;
    }

    .d-md-block {
        display: block !important;
    }
}

/* 平板设备 (768px - 1199px) */
@media (max-width: 1199px) {
    .sites-container {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 14px;
    }
    
    .sites-container.grouped-layout {
        display: block !important;
    }
    
    .search-container {
        margin: 0 20px;
    }
    
    .content-area {
        padding: 16px;
    }
    
    /* 平板分组布局 */
    .category-group-sites {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 14px;
    }
}

/* 中等屏幕 (768px - 991px) */
@media (max-width: 991px) {
    :root {
        --sidebar-width: 240px;
    }
    
    .sites-container {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }
    
    .sites-container.grouped-layout {
        display: block !important;
    }
    
    .navbar-custom {
        padding: 0 16px;
    }
    
    /* 中等屏幕分组布局 */
    .category-group-sites {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 14px;
    }
    
    .search-container {
        margin: 0 16px;
        max-width: 400px;
    }
}

/* 移动设备 (< 768px) */
@media (max-width: 767px) {
    :root {
        --sidebar-width: 280px;
        --navbar-height: 56px;
    }

    /* 导航栏调整 */
    .navbar-custom {
        padding: 0 8px;
        height: var(--navbar-height);
        gap: 8px; /* 确保元素之间有适当间距 */
    }

    .brand-text {
        display: none;
    }

    /* 搜索容器在移动端的优化 */
    .search-container {
        margin: 0;
        flex: 1; /* 让搜索框占用剩余空间 */
        min-width: 0; /* 允许搜索框收缩 */
        max-width: calc(100vw - 160px); /* 为右侧按钮预留足够空间 */
    }
    
    .search-wrapper {
        position: relative;
    }
    
    .search-input {
        height: 36px;
        font-size: 16px; /* 防止iOS缩放 */
        padding: 0 80px 0 36px;
    }

    .search-shortcut {
        display: none;
    }

    .search-filter-btn {
        width: 32px;
        height: 32px;
    }

    /* 导航栏动作区域优化 */
    .navbar-actions {
        display: flex;
        align-items: center;
        gap: 4px; /* 减小按钮间距以节省空间 */
        flex-shrink: 0; /* 防止动作区域被压缩 */
        min-width: 120px; /* 确保至少有足够空间显示关键按钮 */
    }

    /* 隐藏非关键按钮的文字标签 */
    .data-name,
    .theme-name,
    .view-mode-text {
        display: none !important;
    }

    /* 简化数据管理按钮 */
    .data-management-btn .data-dropdown-arrow {
        display: none; /* 隐藏下拉箭头以节省空间 */
    }

    /* 简化主题选择器按钮 */
    .theme-selector-btn .theme-dropdown-arrow {
        display: none; /* 隐藏下拉箭头以节省空间 */
    }

    /* 确保移动端菜单按钮始终可见 */
    .mobile-menu-btn {
        display: flex !important; /* 强制显示 */
        order: 999; /* 确保在最右侧 */
        background-color: var(--primary-color);
        color: white;
        border: 2px solid var(--primary-color);
        flex-shrink: 0 !important; /* 防止被压缩 */
        position: relative !important;
        z-index: 1001 !important; /* 确保在最上层 */
        margin-left: auto !important; /* 推到最右侧 */
    }

    .mobile-menu-btn:hover {
        background-color: var(--primary-hover);
        border-color: var(--primary-hover);
    }

    /* 防止搜索框挤压菜单按钮 */
    .search-container {
        overflow: hidden; /* 防止内容溢出 */
    }

    .search-input {
        max-width: 100%; /* 防止输入框过宽 */
    }

    /* 移动端搜索筛选器 - 使用更高特异性 */
    .search-container .search-filters {
        position: fixed !important;
        top: var(--navbar-height) !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background-color: var(--card-background) !important;
        z-index: 1060 !important; /* 提高z-index确保在所有内容之上 */
        padding: 20px !important;
        overflow-y: auto !important;
        margin-top: 0 !important;
        border-radius: 0 !important;
        border: none !important;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1) !important;
        /* 添加背景模糊效果 */
        backdrop-filter: blur(10px) !important;
        -webkit-backdrop-filter: blur(10px) !important;
        /* 确保完全覆盖屏幕 */
        width: 100vw !important;
        height: 100vh !important;
        max-width: none !important;
        max-height: none !important;
        /* 确保显示在最顶层 */
        transform: translateZ(0) !important;
    }

    /* 移动端搜索筛选器头部 - 使用更高特异性 */
    .search-container .search-filters .search-filters-header {
        position: sticky !important;
        top: 0 !important;
        background-color: var(--card-background) !important;
        z-index: 1061 !important;
        margin: -20px -20px 20px -20px !important;
        padding: 20px 20px 16px 20px !important;
        border-bottom: 1px solid var(--border-color) !important;
        /* 确保头部在移动端正确显示 */
        width: calc(100% + 40px) !important;
        margin-left: -20px !important;
        margin-right: -20px !important;
    }

    /* 移动端筛选器关闭按钮 - 使用更高特异性 */
    .search-container .search-filters .search-filters-close {
        position: absolute !important;
        top: 16px !important;
        right: 16px !important;
        width: 32px !important;
        height: 32px !important;
        border: none !important;
        background: var(--surface-hover) !important;
        border-radius: 50% !important;
        color: var(--text-secondary) !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 16px !important;
        transition: all var(--transition-fast) !important;
        z-index: 1062 !important;
        /* 确保按钮可点击 */
        pointer-events: auto !important;
        touch-action: manipulation !important;
    }

    .search-container .search-filters .search-filters-close:hover {
        background: var(--surface-color) !important;
        color: var(--text-primary) !important;
    }

    /* 移动端筛选器内容区域优化 */
    .search-container .search-filters .search-filters-content {
        gap: 20px !important;
        padding-top: 10px !important;
    }

    .search-container .search-filters .filter-group {
        gap: 12px !important;
    }

    .search-container .search-filters .filter-select {
        height: 44px !important;
        font-size: 16px !important;
        padding: 12px !important;
    }

    .search-container .search-filters .tag-filters {
        gap: 8px !important;
    }

    .search-container .search-filters .tag-filter {
        padding: 8px 12px !important;
        font-size: 14px !important;
        min-height: 36px !important;
        border-radius: 6px !important;
    }

    /* 确保移动端筛选器在所有情况下都能正确显示 */
    .search-container .search-filters[style*="display: block"] {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }


    
    /* 侧边栏移动端适配 */
    .sidebar {
        transform: translateX(-100%);
        width: var(--sidebar-width);
        box-shadow: var(--shadow-lg);
        z-index: 999; /* 确保移动端侧边栏在遮罩层之上 */
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    /* 内容区域调整 */
    .content-area {
        margin-left: 0;
        padding: 12px;
    }
    
    .content-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
    }

    .content-header h2 {
        font-size: 18px;
    }
    
    /* 网站卡片移动端布局 */
    .sites-container {
        grid-template-columns: 1fr;
        gap: 10px;
        margin-bottom: 24px;
    }
    
    .sites-container.grouped-layout {
        display: block !important;
    }
    
    .site-card {
        padding: 12px;
    }
    
    .site-header {
        gap: 8px;
        margin-bottom: 8px;
    }
    
    .site-icon {
        width: 28px;
        height: 28px;
        font-size: 16px;
    }
    
    /* 移动端分组布局 */
    .category-group-sites {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .site-title {
        font-size: 14px;
    }
    
    .site-description {
        font-size: 12px;
        -webkit-line-clamp: 2;
    }
    
    /* 搜索结果移动端调整 */
    .search-results {
        max-height: 60vh;
    }
    
    .search-result-item {
        padding: 10px 12px;
    }
    
    .search-result-tags {
        gap: 3px;
        margin-top: 4px;
    }
    
    .search-result-tag {
        font-size: 9px;
        padding: 1px 4px;
    }
    
    .search-match-badge {
        font-size: 8px;
        padding: 1px 3px;
        margin-left: 4px;
    }
    
    .search-match-badge i {
        font-size: 7px;
    }
    
    /* 移动端图标样式 */
    .site-icon {
        width: 28px;
        height: 28px;
        font-size: 18px;
    }
    
    .site-icon-image {
        width: 100%;
        height: 100%;
    }
    
    .site-icon-fallback {
        font-size: 14px;
    }
    
    /* Markdown 模态框移动端样式 */
    .markdown-modal {
        padding: 10px;
    }
    
    .markdown-modal-content {
        max-height: 95vh;
    }
    
    .markdown-modal-header {
        padding: 16px;
    }
    
    .markdown-modal-title {
        font-size: 18px;
    }
    
    .markdown-modal-body {
        padding: 16px;
    }
    
    .markdown-content h1 {
        font-size: 24px;
    }
    
    .markdown-content h2 {
        font-size: 20px;
    }
    
    .markdown-content h3 {
        font-size: 18px;
    }
    
    /* 移动端菜单按钮 */
    .mobile-menu-btn {
        display: flex !important;
    }

    /* 移动端主题选择器优化 */
    .theme-selector-dropdown {
        position: relative;
    }

    .theme-selector-btn {
        min-width: 40px;
        padding: 8px;
        font-size: 16px;
        /* 防止字体纵向显示 */
        writing-mode: horizontal-tb;
        text-orientation: mixed;
        white-space: nowrap;
        flex-direction: row;
        align-items: center;
        justify-content: center;
    }

    .theme-selector-btn .theme-name {
        display: none; /* 移动端隐藏主题名称文字 */
    }

    .theme-dropdown-arrow {
        margin-left: 4px;
        font-size: 10px;
    }

    /* 移动端主题下拉菜单 */
    .theme-dropdown-menu {
        position: fixed;
        top: var(--navbar-height);
        left: 12px;
        right: 12px;
        min-width: auto;
        max-width: none;
        margin-top: 8px;
        z-index: 1050;
        max-height: calc(100vh - var(--navbar-height) - 20px);
        overflow-y: auto;
    }

    /* 移动端主题选项优化 */
    .theme-option {
        padding: 16px 20px;
        margin: 4px 8px;
        border-radius: 12px;
        /* 确保文字水平显示 */
        writing-mode: horizontal-tb;
        text-orientation: mixed;
        flex-direction: row;
        align-items: center;
        min-height: 60px;
    }

    .theme-option-icon {
        font-size: 20px;
        width: 28px;
        flex-shrink: 0;
    }

    .theme-option-info {
        flex: 1;
        text-align: left;
    }

    .theme-option-name {
        font-size: 16px;
        font-weight: 600;
        line-height: 1.2;
        /* 强制水平文字方向 */
        writing-mode: horizontal-tb !important;
        text-orientation: mixed !important;
    }

    .theme-option-desc {
        font-size: 14px;
        line-height: 1.3;
        margin-top: 4px;
        /* 强制水平文字方向 */
        writing-mode: horizontal-tb !important;
        text-orientation: mixed !important;
    }
    
    /* 分类导航移动端优化 */
    .category-link {
        padding: 10px 16px;
        font-size: 14px;
        /* 确保移动端可以正常点击 */
        pointer-events: auto;
        touch-action: manipulation;
        user-select: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
        min-height: 44px; /* 符合移动端最小触摸目标尺寸 */
    }

    .subcategory-link {
        padding: 8px 16px 8px 36px;
        font-size: 13px;
        /* 确保子分类链接也可以正常点击 */
        pointer-events: auto;
        touch-action: manipulation;
        user-select: none;
        min-height: 40px;
    }

    /* 确保分类列表容器不会阻止点击事件 */
    .category-list {
        pointer-events: auto;
    }

    .category-nav {
        pointer-events: auto;
    }

    /* 确保侧边栏在移动端打开时可以正常交互 */
    .sidebar.active {
        pointer-events: auto;
        touch-action: pan-y; /* 允许垂直滚动 */
    }
}

/* 小屏幕移动设备 (< 480px) */
@media (max-width: 479px) {
    .navbar-custom {
        padding: 0 4px; /* 进一步减少内边距 */
        gap: 4px; /* 减小元素间距 */
    }

    .search-container {
        margin: 0;
        max-width: calc(100vw - 140px); /* 为右侧按钮预留更多空间 */
    }

    /* 进一步优化导航栏动作区域 */
    .navbar-actions {
        gap: 2px; /* 最小间距 */
        min-width: 100px; /* 减少最小宽度要求 */
    }

    /* 隐藏帮助按钮以节省空间 */
    .help-btn {
        display: none !important;
    }

    /* 隐藏视图切换按钮以节省空间 */
    .view-toggle-btn {
        display: none !important;
    }

    /* 简化主题选择器 - 只保留图标 */
    .theme-selector-btn {
        width: 36px;
        height: 36px;
        padding: 0;
    }

    /* 简化数据管理按钮 - 只保留图标 */
    .data-management-btn {
        width: 36px;
        height: 36px;
        padding: 0;
    }

    /* 强化移动端菜单按钮显示 */
    .mobile-menu-btn {
        width: 40px !important;
        height: 40px !important;
        background-color: var(--primary-color) !important;
        color: white !important;
        border: 2px solid var(--primary-color) !important;
        border-radius: var(--radius-md) !important;
        font-size: 16px !important;
        z-index: 1001 !important; /* 确保在最上层 */
    }
    
    .content-area {
        padding: 12px;
    }
    
    .sites-container {
        gap: 10px;
    }
    
    .sites-container.grouped-layout {
        display: block !important;
    }
    
    .site-card {
        padding: 14px;
    }
    
    .site-header {
        gap: 8px;
    }
    
    .site-icon {
        width: 32px;
        height: 32px;
        font-size: 18px;
    }
    
    .site-title {
        font-size: 14px;
    }
    
    .site-description {
        font-size: 12px;
    }
    
    .site-tags {
        gap: 4px;
        margin-top: 10px;
    }
    
    .site-tag {
        font-size: 10px;
        padding: 2px 6px;
    }
}

/* 超小屏幕设备 (< 360px) - 针对老旧手机优化 */
@media (max-width: 359px) {
    .navbar-custom {
        padding: 0 2px; /* 最小内边距 */
        gap: 2px;
    }

    .search-container {
        max-width: calc(100vw - 120px); /* 为右侧按钮预留更多空间 */
    }

    .search-input {
        font-size: 14px; /* 减小字体以节省空间 */
        padding: 0 60px 0 30px; /* 调整内边距 */
    }

    /* 只保留最关键的按钮 */
    .data-management-dropdown,
    .theme-selector-dropdown {
        display: none !important; /* 隐藏非关键功能 */
    }

    /* 确保移动端菜单按钮在超小屏幕上也能正常显示 */
    .mobile-menu-btn {
        width: 44px !important; /* 符合触摸目标最小尺寸 */
        height: 44px !important;
        font-size: 18px !important;
        position: relative !important;
        right: 0 !important;
    }

    /* 品牌logo也适当缩小 */
    .logo {
        width: 28px;
        height: 28px;
    }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1400px) {
    .sites-container {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 18px;
    }
    
    .sites-container.grouped-layout {
        display: block !important;
    }
    
    .content-area {
        padding: 24px;
    }
    
    .search-container {
        max-width: 600px;
    }
    
    /* 高分辨率分组布局 */
    .category-group-sites {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 18px;
    }
}

/* 横屏移动设备 */
@media (max-width: 767px) and (orientation: landscape) {
    .sites-container {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
    
    .sites-container.grouped-layout {
        display: block !important;
    }
    
    .sidebar {
        width: 260px;
    }
    
    /* 横屏移动端分组布局 */
    .category-group-sites {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 14px;
    }
}

/* 减少动画的用户偏好设置 */
@media (prefers-reduced-motion: reduce) {
    * {
        transition-duration: 0.01ms !important;
        transition-delay: 0.01ms !important;
        animation-duration: 0.01ms !important;
        animation-delay: -0.01ms !important;
        animation-iteration-count: 1 !important;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }
    
    [data-theme="dark"] {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
    }
}

/* 深色模式系统偏好 - 仅在没有手动设置主题时生效 */
@media (prefers-color-scheme: dark) {
    body:not([data-theme]) {
        --background-color: #0f172a;
        --surface-color: #1e293b;
        --surface-hover: #334155;
        --card-background: #1e293b;
        --text-primary: #f8fafc;
        --text-secondary: #cbd5e1;
        --text-muted: #94a3b8;
        --border-color: #334155;
        --border-hover: #475569;
    }
}

/* 分类分组响应式样式 */
@media (max-width: 768px) {
    .category-group {
        margin-bottom: 18px;
    }

    .category-group-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
        margin-bottom: 10px;
    }
    
    .category-group-title {
        font-size: 16px;
    }
    
    .category-group-sites {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 14px;
    }
}

@media (max-width: 480px) {
    .category-group {
        margin-bottom: 16px;
    }

    .category-group-header {
        padding-bottom: 4px;
        margin-bottom: 8px;
    }

    .category-group-title {
        font-size: 15px;
    }

    .category-group-count {
        font-size: 12px;
        padding: 3px 8px;
    }

    .category-group-sites {
        grid-template-columns: 1fr;
        gap: 12px;
    }
}