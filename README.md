# FaciShare 导航

一个现代化的企业级导航页面，基于原生JavaScript构建，提供智能搜索、多主题切换、响应式设计和完整的状态管理功能。

![NavSphere](https://img.shields.io/badge/NavSphere-v2.0-blue) ![License](https://img.shields.io/badge/license-MIT-green) ![JavaScript](https://img.shields.io/badge/JavaScript-ES6+-yellow) ![CSS](https://img.shields.io/badge/CSS-Grid%20%26%20Flexbox-blue) ![PWA](https://img.shields.io/badge/PWA-Ready-purple)

## 📖 详细文档

完整的项目文档已经被拆分到 `docs` 目录中，以提供更清晰的结构和更好的可读性。

**[➡️ 点击这里查看完整文档](./docs/index.md)**

## 🌟 核心特性

- **🎯 智能导航系统**: 多级分类、状态记忆、访问统计、Markdown渲染和多链接卡片。
- **🔍 高级搜索引擎**: 实时搜索、智能权重、标签筛选和快捷键支持。
- **🎨 多主题系统**: 4套精美主题、时间自动切换和系统偏好跟随。
- **⏰ 时间范围提示系统**: 智能时间检测、配置文件驱动和非阻塞提示。
- **📱 响应式设计**: 移动端优化、自适应布局和性能优化。
- **📦 可视化编辑**: 提供一个独立的 `npve.html` 用于可视化编辑导航数据。

## 🚀 快速开始

1. **克隆项目**
   ```bash
   git clone https://git.firstshare.cn/devops/fs-oss-navigation.git
   cd fs-oss-navigation
   ```

2. **启动本地服务器**
   ```bash
   # 使用Python (推荐)
   python -m http.server 8000
   ```

3. **访问应用**
   打开浏览器访问 `http://localhost:8000`

## 🤝 贡献

欢迎通过提交 Issue 或 Pull Request 来为项目做出贡献。请参考 [贡献指南](./docs/contributing.md)。

## 🛡️ 许可证

本项目基于 [MIT License](./docs/contributing.md#️-许可证) 开源。