# 🌟 核心特性

### 🎯 智能导航系统
- **多级分类结构** - 支持3级分类嵌套，灵活的内容组织方式
- **智能状态记忆** - 自动保存分类展开状态和当前浏览位置
- **访问统计** - 内置访问记录管理，支持使用频率分析
- **Markdown文档** - 支持本地文档渲染，丰富的内容展示
- **🔗 多链接卡片** - 每个卡片支持网址链接和文档链接的灵活组合

### 🔍 高级搜索引擎
- **实时搜索** - 300ms防抖优化，支持多字段匹配
- **智能权重** - 标题、描述、标签、分类的差异化权重排序
- **标签筛选** - 支持标签精确匹配和部分匹配，高亮显示
- **快捷键支持** - `⌘K`/`Ctrl+K` 快速唤起，键盘导航

### 🎨 多主题系统
- **4套精美主题** - 日光象牙白、夜月玄玉黑、清雅茉莉绿、深邃海军蓝
- **时间自动切换** - 可配置时间段自动切换主题
- **系统偏好跟随** - 自动检测系统深色模式偏好
- **平滑过渡动画** - 主题切换无闪烁，视觉体验流畅

### ⏰ 时间范围提示系统
- **智能时间检测** - 支持每日时间范围和绝对时间范围两种模式
- **配置文件驱动** - 通过JSON配置文件管理所有提示设置
- **预设配置** - 内置学生作息、办公室工作等预设配置
- **非阻塞提示** - Toast风格提示，不影响用户正常操作
- **首次检测机制** - 基于时间范围和内容的智能首次提示判定

### 📱 响应式设计
- **移动端优化** - 抽屉式侧边栏，触摸友好的交互设计
- **自适应布局** - CSS Grid + Flexbox，完美适配各种屏幕
- **性能优化** - 懒加载、防抖搜索、事件委托等优化策略

## 🔧 功能详解

### 🧭 智能导航系统

#### 多级分类结构

- **3级分类支持** - 主分类 → 子分类 → 具体分类的层级结构
- **智能展开逻辑** - 点击分类名称查看内容，点击箭头展开子分类
- **状态持久化** - 自动保存分类展开状态和当前浏览位置
- **父分类聚合** - 父分类显示所有子分类的网站内容

#### 访问统计管理

- **访问记录** - 自动记录网站访问次数和时间
- **使用频率分析** - 基于访问数据的智能排序
- **数据持久化** - 本地存储访问历史数据

### 🔍 高级搜索引擎

#### 搜索算法

- **多字段匹配** - 网站名称、描述、标签、分类名称、URL
- **智能权重排序** - 标题匹配(100分) > 描述匹配(40分) > 标签匹配(60分)
- **防抖优化** - 300ms防抖，减少不必要的搜索请求
- **实时高亮** - 搜索结果中匹配内容高亮显示

#### 交互体验

- **快捷键支持** - `⌘K`/`Ctrl+K` 快速唤起搜索
- **键盘导航** - 方向键选择结果，回车打开链接
- **匹配类型标识** - 显示匹配类型徽章（标题、描述、标签等）
- **搜索状态管理** - 智能的搜索状态切换和重置

### 🎨 多主题系统

#### 主题切换策略

**优先级顺序**：手动设置 > 时间自动 > 系统偏好 > 默认主题

#### 时间自动切换

- **智能时间检测** - 页面加载时根据当前时间判断主题
- **预设配置** - 标准、早起、夜猫子、办公等多种时间预设
- **自定义时间段** - 支持自定义浅色/深色主题时间段
- **无性能损耗** - 仅在页面加载时判断，不持续监听时间

#### 视觉效果

- **平滑过渡** - 主题切换时的平滑动画效果
- **防闪烁加载** - 页面加载时避免主题闪烁
- **一致性保证** - 所有UI组件的主题一致性