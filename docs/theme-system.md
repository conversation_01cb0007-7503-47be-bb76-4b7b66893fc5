# 🎨 主题系统

### 🌈 可用主题

| 主题名称 | 主题ID | 设计理念 | 适用场景 |
|---------|--------|----------|----------|
| 日光象牙白 | `ivory-light` | 温暖舒适的浅色调 | 日间办公、阅读 |
| 夜月玄玉黑 | `dark-obsidian` | 深邃优雅的深色调 | 夜间使用、护眼 |
| 清雅茉莉绿 | `jasmine-green` | 禅意美学、护眼悦目 | 长时间使用 |
| 深邃海军蓝 | `navy-blue` | 沉稳专业的蓝色调 | 商务场景 |

### ⏰ 时间自动切换

#### 工作原理

- **页面加载判断** - 仅在页面加载时根据当前时间判断主题
- **性能优化** - 不持续监听时间变化，避免性能损耗
- **智能优先级** - 手动设置 > 时间自动 > 系统偏好 > 默认主题

#### 预设配置

| 预设名称 | 浅色时间段 | 深色时间段 | 适用人群 |
|---------|-----------|-----------|----------|
| `standard` | 6:00-18:00 | 18:00-6:00 | 标准作息 |
| `earlyBird` | 5:00-19:00 | 19:00-5:00 | 早起型用户 |
| `nightOwl` | 8:00-20:00 | 20:00-8:00 | 夜猫子型用户 |
| `office` | 9:00-17:00 | 17:00-9:00 | 办公时间 |

### 📱 快捷键支持

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `⌘K` / `Ctrl+K` | 唤起搜索 | 全局搜索快捷键 |
| `Escape` | 清空搜索/关闭侧边栏 | 重置界面状态 |
| `⌘Esc` / `Ctrl+Esc` | 重置主题配置 | 恢复默认主题设置 |
| `↑/↓` | 搜索结果导航 | 键盘选择搜索结果 |
| `Enter` | 选择搜索结果 | 打开选中的网站 |
| `?` | 显示帮助信息 | 查看当前主题和配置 |

**平台适配**：快捷键自动适配操作系统（macOS使用⌘键，Windows/Linux使用Ctrl键）

## 🎨 自定义样式

### 修改主题色彩

编辑 `css/style.css` 中的CSS变量：

```css
:root {
    --primary-color: #3b82f6;      /* 主色调 */
    --background-color: #ffffff;    /* 背景色 */
    --text-primary: #1e293b;       /* 主文字色 */
    /* ... 更多变量 */
}
```

### 自定义深色主题

编辑 `css/themes.css` 中的深色主题变量：

```css
[data-theme="dark"] {
    --background-color: #0f172a;
    --text-primary: #f8fafc;
    /* ... 深色主题变量 */
}
```

### 🔗 自定义多链接卡片样式

编辑 `css/style.css` 中的卡片样式：

```css
/* 自定义外部链接卡片样式 */
.site-card-external {
    border-left: 3px solid #10b981;  /* 绿色边框 */
    background: linear-gradient(135deg, var(--card-background) 0%, #f0fdf4 100%);
}

/* 自定义文档卡片样式 */
.site-card-markdown {
    border-left: 3px solid #f59e0b;  /* 橙色边框 */
    background: linear-gradient(135deg, var(--card-background) 0%, #fffbeb 100%);
}

/* 自定义双链接卡片样式 */
.site-card-both {
    border-left: 3px solid #3b82f6;  /* 蓝色边框 */
    background: linear-gradient(135deg, var(--card-background) 0%, #eff6ff 100%);
}

/* 自定义文档指示器样式 */
.doc-indicator {
    background-color: #f59e0b;       /* 橙色背景 */
    color: white;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.doc-indicator:hover {
    background-color: #d97706;       /* 悬停时深橙色 */
    transform: scale(1.05);
}

/* 紧凑模式下的文档指示器 */
.compact-view .doc-indicator {
    padding: 2px 4px;
    border-radius: 50%;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.compact-view .doc-indicator span {
    display: none;  /* 隐藏文字，只显示图标 */
}
```

### 🎨 卡片类型指示器自定义

```css
/* 卡片右上角的类型指示器 */
.site-card::after {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    opacity: 0.6;
}

/* 不同类型的指示器颜色 */
.site-card-external::after {
    background-color: #10b981;  /* 绿色 - 外部链接 */
}

.site-card-markdown::after {
    background-color: #f59e0b;  /* 橙色 - 文档 */
}

.site-card-both::after {
    background-color: #3b82f6;  /* 蓝色 - 双链接 */
}

.site-card-no-link::after {
    background-color: #6b7280;  /* 灰色 - 无链接 */
    opacity: 0.3;
}