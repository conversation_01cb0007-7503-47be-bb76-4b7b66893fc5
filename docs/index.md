# FaciShare 导航

一个现代化的企业级导航页面，基于原生JavaScript构建，提供智能搜索、多主题切换、响应式设计和完整的状态管理功能。

![NavSphere](https://img.shields.io/badge/NavSphere-v2.0-blue) ![License](https://img.shields.io/badge/license-MIT-green) ![JavaScript](https://img.shields.io/badge/JavaScript-ES6+-yellow) ![CSS](https://img.shields.io/badge/CSS-Grid%20%26%20Flexbox-blue) ![PWA](https://img.shields.io/badge/PWA-Ready-purple)

## 📖 目录

- [核心特性](./features.md)
- [技术架构](#-技术架构)
- [快速开始](./getting-started.md)
- [配置指南](./configuration.md)
- [时间范围提示配置](./time-notifications.md)
- [功能详解](./features.md#功能详解)
- [主题系统](./theme-system.md)
- [开发指南](./development.md)
- [部署方案](./getting-started.md#部署方案)
- [性能优化](./development.md#性能优化)
- [故障排除](./development.md#故障排除)
- [API文档](./api.md)
- [时间范围提示API](./time-notifications.md#时间范围提示api)
- [贡献指南](./contributing.md)
- [导航页数据可视化编辑](./visual-editor.md)

## 🏗️ 技术架构

### 📁 项目结构

```
fs-oss-navigation/
├── index.html             # 主页面入口
|── npve.html              # 导航页数据可视化编辑页面
├── nav/css/               # 样式系统
│   ├── style.css          # 核心样式和CSS变量
│   ├── themes.css         # 多主题样式定义
│   └── responsive.css     # 响应式布局
├── nav/js/                # JavaScript模块
│   ├── app.js             # 主应用控制器
│   ├── search.js          # 搜索引擎
│   ├── theme.js           # 主题管理器
│   ├── sidebar.js         # 侧边栏管理器
│   ├── markdown.js        # Markdown渲染器
│   ├── visit-manager.js   # 访问统计管理
│   ├── time-notification.js # 时间范围提示管理器
│   ├── config.js          # 全局配置
│   ├── platform.js        # 平台检测工具
│   └── utils.js           # 通用工具函数
├── nav/data/              # 数据层
│   ├── appconfig.json     # 应用核心配置
│   ├── sites.json         # 网站数据配置
│   ├── time-notifications.json # 时间范围提示配置
│   └── docs/              # Markdown文档
├── nav/assets/            # 静态资源
│   └── icons/             # 图标资源
└── README.md              # 项目文档
```

### 🔧 核心架构

```
┌─────────────────────────────────────────┐
│                NavApp                   │  ← 主应用控制器
├─────────────────────────────────────────┤
│  ThemeManager  │  SearchManager         │  ← 功能管理器
│  SidebarManager│  MarkdownManager       │
│  VisitManager  │  TimeNotificationMgr   │
│  Platform Utils│  Config Manager        │
├─────────────────────────────────────────┤
│  DOM操作层     │  事件处理层               │  ← 底层服务
│  数据处理层    │  工具函数层                │
└─────────────────────────────────────────┘
```

### 🎯 设计模式

- **单例模式** - 主应用实例和管理器实例
- **观察者模式** - 主题变化通知和状态更新
- **策略模式** - 主题切换策略和搜索匹配策略
- **工厂模式** - DOM元素创建和事件处理器
- **模块模式** - 功能封装和命名空间管理