# ⏰ 时间范围提示配置

时间范围提示功能通过配置文件系统管理，支持在指定时间范围内显示首次提示。

#### 核心配置文件

**1. 应用配置 (`nav/data/appconfig.json`)**

```json
{
  "timeNotifications": {
    "enabled": true,
    "configPath": "data/time-notifications.json",
    "description": "时间范围提示配置",
    "autoLoad": true,
    "fallbackEnabled": false
  }
}
```

**配置属性说明**：
- `enabled`: 是否启用时间范围提示功能（布尔值）
- `configPath`: 时间提示配置文件的路径（字符串）
- `description`: 配置描述信息（字符串）
- `autoLoad`: 是否在应用启动时自动加载配置（布尔值）
- `fallbackEnabled`: 配置文件加载失败时是否启用降级配置（布尔值）

**2. 时间提示配置 (`data/time-notifications.json`)**

```json
{
  "version": "1.0",
  "config": {
    "enabled": true,
    "checkInterval": 30000,
    "defaultType": "info",
    "autoStart": true
  },
  "notifications": [
    {
      "id": "evening-reminder",
      "name": "晚间休息提醒",
      "type": "daily",
      "timeRange": {
        "start": "22:00",
        "end": "23:00"
      },
      "content": {
        "title": "晚间提醒",
        "message": "现在是晚间时段，建议适度使用电子设备，注意休息。",
        "type": "info"
      },
      "enabled": true,
      "description": "每日晚间休息提醒"
    }
  ],
  "presets": {
    "student": {
      "name": "学生作息",
      "description": "适合学生的作息提醒配置",
      "notifications": []
    },
    "office": {
      "name": "办公室工作",
      "description": "适合办公室工作的提醒配置",
      "notifications": []
    }
  }
}
```

**配置文件结构说明**：

**基础信息**：
- `version`: 配置文件版本号（字符串）
- `description`: 配置文件描述（可选，字符串）
- `lastUpdated`: 最后更新时间（可选，字符串）

**全局配置 (`config`)**：
- `enabled`: 是否启用时间范围提示功能（布尔值）
- `checkInterval`: 时间检查间隔，单位毫秒（数字，默认30000）
- `defaultType`: 默认提示类型（字符串：'info'|'success'|'warning'|'error'）
- `autoStart`: 是否自动开始检查（布尔值）

**通知配置 (`notifications`)**：
- `id`: 通知唯一标识符（字符串，必需）
- `name`: 通知显示名称（字符串，可选）
- `type`: 时间类型（字符串：'daily'|'absolute'）
- `timeRange`: 时间范围配置（对象）
  - `start`: 开始时间（字符串，格式见下方说明）
  - `end`: 结束时间（字符串，格式见下方说明）
- `content`: 提示内容配置（对象）
  - `title`: 提示标题（字符串，可选）
  - `message`: 提示消息（字符串，必需）
  - `type`: 提示类型（字符串：'info'|'success'|'warning'|'error'）
- `enabled`: 是否启用此通知（布尔值）
- `description`: 通知描述（字符串，可选）

**预设配置 (`presets`)**：
- 预设名称作为键（如 `student`、`office`）
- `name`: 预设显示名称（字符串）
- `description`: 预设描述（字符串）
- `notifications`: 预设包含的通知配置数组（数组）

#### 时间格式说明

**每日时间范围** (使用 HH:MM 格式)：
```json
{
  "type": "daily",
  "timeRange": {
    "start": "09:00",
    "end": "17:30"
  }
}
```
start：上午9点
end：下午5点30分

**属性说明**：
- `type`: 必须设置为 `"daily"`，表示每日重复的时间范围
- `start`: 开始时间，格式为 `HH:MM`（24小时制）
- `end`: 结束时间，格式为 `HH:MM`（24小时制）
- **特点**: 每天在指定时间范围内首次进入页面时显示提示

**跨天时间范围**：
```json
{
  "type": "daily",
  "timeRange": {
    "start": "23:30",
    "end": "02:00"
  }
}
```
start：晚上11点30分
end：次日凌晨2点

**属性说明**：
- `type`: 设置为 `"daily"`，支持跨天时间范围
- `start`: 开始时间（当天）
- `end`: 结束时间（次日），当 `end` < `start` 时自动识别为跨天
- **特点**: 系统自动处理跨天逻辑，如23:30-02:00表示从当天23:30到次日02:00

**绝对时间范围** (使用 ISO 8601 格式)：
```json
{
  "type": "absolute",
  "timeRange": {
    "start": "2025-07-29T20:00:00",
    "end": "2025-07-29T22:00:00"
  }
}
```

**属性说明**：
- `type`: 必须设置为 `"absolute"`，表示绝对时间范围
- `start`: 开始时间，ISO 8601格式 `YYYY-MM-DDTHH:MM:SS`
- `end`: 结束时间，ISO 8601格式 `YYYY-MM-DDTHH:MM:SS`
- **特点**: 在整个时间范围内只显示一次，适用于特定活动或事件提醒

#### 配置管理API

**配置文件操作**：

```javascript
// 查看当前配置 - 在控制台显示完整配置信息
TimeNotificationConfig.showConfig();
// 返回: void，在控制台输出配置详情

// 重新加载配置文件 - 从文件重新加载配置
await TimeNotificationConfig.reloadConfig();
// 返回: Promise<boolean> - 成功返回true，失败返回false

// 查看可用预设 - 获取所有预设配置列表
await TimeNotificationConfig.getAvailablePresets();
// 返回: Promise<Array> - 预设配置数组，每个元素包含 {id, name, description, notificationCount}

// 加载预设配置 - 应用指定的预设配置
await TimeNotificationConfig.loadPreset('student');  // 学生作息
await TimeNotificationConfig.loadPreset('office');   // 办公室工作
// 参数: presetName (string) - 预设名称
// 返回: Promise<boolean> - 成功返回true，失败返回false
```

**通知信息获取**：

```javascript
// 获取所有通知配置
const notifications = TimeNotificationConfig.getAllNotifications();
// 返回: Array - 所有通知配置的数组

// 获取启用的通知配置
const enabled = TimeNotificationConfig.getEnabledNotifications();
// 返回: Array - 仅包含enabled为true的通知配置
```

**运行时管理**：

```javascript
// 运行时添加通知
TimeNotificationConfig.addNotification({
  id: 'custom-reminder',           // 必需: 唯一标识符
  type: 'daily',                   // 必需: 'daily' 或 'absolute'
  timeRange: {                     // 必需: 时间范围配置
    start: '20:00',
    end: '20:30'
  },
  content: {                       // 必需: 提示内容配置
    title: '自定义提醒',           // 可选: 提示标题
    message: '这是一个自定义提醒',  // 必需: 提示消息
    type: 'info'                   // 可选: 提示类型，默认'info'
  },
  enabled: true                    // 可选: 是否启用，默认true
});
// 返回: string|null - 成功返回通知ID，失败返回null

// 移除通知
TimeNotificationConfig.removeNotification('notification-id');
// 参数: notificationId (string) - 要移除的通知ID
// 返回: boolean - 成功返回true，失败返回false

// 启用/禁用功能
TimeNotificationConfig.setEnabled(true);   // 启用
TimeNotificationConfig.setEnabled(false);  // 禁用
// 参数: enabled (boolean) - 是否启用

// 清除所有显示记录（重新触发提醒）
TimeNotificationConfig.clearAllRecords();
// 返回: void - 清除localStorage中的所有显示记录
```

#### 使用示例

**基础配置示例**：

**1. 每日时间范围提示**：
```javascript
// 每日时间范围提示 - 晚间休息提醒
TimeNotificationConfig.addNotification({
    id: 'evening-rest-reminder',        // 唯一标识符，用于管理此通知
    type: 'daily',                      // 每日重复类型
    timeRange: {
        start: '22:00',                 // 每天晚上10点开始
        end: '23:00'                    // 每天晚上11点结束
    },
    content: {
        title: '晚间休息提醒',          // 提示标题，显示在通知顶部
        message: '现在是晚间时段，建议适度使用电子设备，注意休息。',  // 提示内容
        type: 'info'                    // 提示类型：info(蓝色)、success(绿色)、warning(黄色)、error(红色)
    },
    enabled: true                       // 启用此通知
});
```

**2. 绝对时间范围提示**：
```javascript
// 绝对时间范围提示 - 特定活动提醒
TimeNotificationConfig.addNotification({
    id: 'special-event-2025',          // 特定事件的唯一标识
    type: 'absolute',                   // 绝对时间类型，只在指定时间段内有效
    timeRange: {
        start: '2025-07-29T20:00:00',   // 具体开始时间（ISO 8601格式）
        end: '2025-07-29T22:00:00'      // 具体结束时间（ISO 8601格式）
    },
    content: {
        title: '特别活动提醒',          // 活动标题
        message: '特别活动正在进行中，不要错过精彩内容！',  // 活动详情
        type: 'warning'                 // 使用警告样式突出显示
    },
    enabled: true                       // 启用此活动提醒
});
```

**3. 跨天时间范围提示**：
```javascript
// 跨天时间范围提示 - 深夜学习提醒
TimeNotificationConfig.addNotification({
    id: 'late-night-study',            // 深夜学习提醒标识
    type: 'daily',                      // 每日重复，但跨越两天
    timeRange: {
        start: '23:30',                 // 当天23:30开始
        end: '02:00'                    // 次日02:00结束（系统自动识别跨天）
    },
    content: {
        title: '深夜学习提醒',          // 健康提醒标题
        message: '深夜学习要注意身体健康，适当休息很重要。',  // 健康建议
        type: 'warning'                 // 使用警告样式提醒注意健康
    },
    enabled: true                       // 启用健康提醒
});
```

**配置属性详解**：
- `id`: 通知的唯一标识符，用于后续的管理操作（修改、删除等）
- `type`: 时间类型，`'daily'`表示每日重复，`'absolute'`表示特定时间段
- `timeRange.start`: 开始时间，daily类型使用HH:MM格式，absolute类型使用ISO 8601格式
- `timeRange.end`: 结束时间，格式同start，daily类型支持跨天（end < start）
- `content.title`: 可选的提示标题，显示在通知的顶部
- `content.message`: 必需的提示消息，通知的主要内容
- `content.type`: 提示样式类型，影响通知的颜色和图标
- `enabled`: 是否启用此通知，false时不会触发提示

#### 配置验证规则

**必需字段验证**：
- `id`: 必须是非空字符串，且在所有通知中唯一
- `type`: 必须是 `'daily'` 或 `'absolute'`
- `timeRange`: 必须包含 `start` 和 `end` 字段
- `content.message`: 必须是非空字符串

**时间格式验证**：
- **daily类型**: `start` 和 `end` 必须是 `HH:MM` 格式（如 `09:30`）
- **absolute类型**: `start` 和 `end` 必须是有效的 ISO 8601 格式（如 `2025-07-29T20:00:00`）
- **时间范围**: `end` 时间必须晚于 `start` 时间（daily类型支持跨天例外）

**可选字段默认值**：
- `name`: 默认使用 `id` 值
- `content.title`: 默认为空，不显示标题
- `content.type`: 默认为 `'info'`
- `enabled`: 默认为 `true`
- `description`: 默认为空

**快速设置函数**：

```javascript
// 学生作息提醒设置
function setupStudentSchedule() {
    const notifications = [
        {
            id: 'morning-class',
            timeRange: { start: '08:00', end: '08:30' },
            content: { title: '早课提醒', message: '早课时间到了，准备上课！', type: 'info' }
        },
        {
            id: 'sleep-reminder',
            timeRange: { start: '22:30', end: '23:00' },
            content: { title: '睡眠提醒', message: '该准备休息了，充足睡眠很重要！', type: 'warning' }
        }
    ];

    notifications.forEach(notification => {
        TimeNotificationConfig.addNotification({
            ...notification,
            type: 'daily',
            enabled: true
        });
    });
}

// 办公室工作提醒设置
function setupOfficeSchedule() {
    const notifications = [
        {
            id: 'work-start',
            timeRange: { start: '09:00', end: '09:30' },
            content: { title: '工作开始', message: '新的工作日开始，查看今天的任务！', type: 'success' }
        },
        {
            id: 'work-end',
            timeRange: { start: '18:00', end: '18:30' },
            content: { title: '下班时间', message: '工作结束，记得整理今天的工作成果。', type: 'success' }
        }
    ];

    notifications.forEach(notification => {
        TimeNotificationConfig.addNotification({
            ...notification,
            type: 'daily',
            enabled: true
        });
    });
}