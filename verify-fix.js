// 验证三级导航修复的脚本
// 在浏览器控制台中运行此脚本来验证修复效果

console.log('🔍 开始验证三级导航修复...');

// 1. 检查数据结构
function verifyDataStructure() {
    console.log('\n📊 验证数据结构...');
    
    if (!window.navApp || !window.navApp.data) {
        console.error('❌ NavApp 或数据未加载');
        return false;
    }
    
    const cloudCategory = window.navApp.data.categories.find(cat => cat.id === 'foneshare-cloud');
    if (!cloudCategory) {
        console.error('❌ 未找到多云管理分类');
        return false;
    }
    
    console.log('✅ 找到多云管理分类:', cloudCategory.name);
    
    if (!cloudCategory.children || cloudCategory.children.length === 0) {
        console.error('❌ 多云管理分类没有子分类');
        return false;
    }
    
    console.log('✅ 多云管理有', cloudCategory.children.length, '个二级分类');
    
    let totalThirdLevel = 0;
    let totalSites = 0;
    
    cloudCategory.children.forEach(child => {
        console.log('  📁', child.name, '(', child.id, ')');
        if (child.children) {
            child.children.forEach(grandChild => {
                const siteCount = grandChild.sites ? grandChild.sites.length : 0;
                console.log('    📂', grandChild.name, '- ', siteCount, '个网站');
                totalThirdLevel++;
                totalSites += siteCount;
            });
        }
    });
    
    console.log('✅ 总计:', totalThirdLevel, '个三级分类,', totalSites, '个网站');
    return true;
}

// 2. 检查渲染函数
function verifyRenderFunctions() {
    console.log('\n🔧 验证渲染函数...');
    
    if (!window.navApp.renderGroupedSites) {
        console.error('❌ renderGroupedSites 函数不存在');
        return false;
    }
    
    console.log('✅ renderGroupedSites 函数存在');
    
    // 检查函数是否支持多级嵌套
    const functionStr = window.navApp.renderGroupedSites.toString();
    if (functionStr.includes('hasGrandChildren') && functionStr.includes('parent-group')) {
        console.log('✅ renderGroupedSites 支持多级嵌套');
    } else {
        console.error('❌ renderGroupedSites 不支持多级嵌套');
        return false;
    }
    
    return true;
}

// 3. 检查CSS样式
function verifyCSSStyles() {
    console.log('\n🎨 验证CSS样式...');
    
    const requiredClasses = [
        'category-group',
        'parent-group',
        'child-group',
        'parent-header',
        'child-header',
        'parent-title',
        'child-title'
    ];
    
    let allStylesExist = true;
    
    requiredClasses.forEach(className => {
        const elements = document.querySelectorAll(`.${className}`);
        const hasStyle = getComputedStyle(document.body).getPropertyValue('--primary-color') || 
                        document.styleSheets.length > 0;
        
        if (hasStyle) {
            console.log('✅ CSS类', className, '样式已定义');
        } else {
            console.warn('⚠️  CSS类', className, '可能未定义');
        }
    });
    
    return allStylesExist;
}

// 4. 模拟点击测试
function simulateClickTest() {
    console.log('\n🖱️  模拟点击测试...');
    
    // 查找多云管理的侧边栏链接
    const cloudLink = document.querySelector('[data-category-id="foneshare-cloud"]');
    if (!cloudLink) {
        console.error('❌ 未找到多云管理侧边栏链接');
        return false;
    }
    
    console.log('✅ 找到多云管理侧边栏链接');
    
    // 模拟点击
    try {
        cloudLink.click();
        console.log('✅ 成功点击多云管理分类');
        
        // 等待渲染完成后检查结果
        setTimeout(() => {
            const parentGroups = document.querySelectorAll('.parent-group');
            const childGroups = document.querySelectorAll('.child-group');
            
            console.log('📊 渲染结果:');
            console.log('  - 父级分组:', parentGroups.length, '个');
            console.log('  - 子级分组:', childGroups.length, '个');
            
            if (parentGroups.length >= 2 && childGroups.length >= 6) {
                console.log('🎉 修复成功！三级导航正常显示');
            } else {
                console.warn('⚠️  可能存在问题，请手动检查显示效果');
            }
        }, 500);
        
    } catch (error) {
        console.error('❌ 点击测试失败:', error);
        return false;
    }
    
    return true;
}

// 主验证函数
function runVerification() {
    console.log('🚀 三级导航修复验证开始');
    console.log('='.repeat(50));
    
    const results = {
        dataStructure: verifyDataStructure(),
        renderFunctions: verifyRenderFunctions(),
        cssStyles: verifyCSSStyles(),
        clickTest: simulateClickTest()
    };
    
    console.log('\n📋 验证结果汇总:');
    console.log('='.repeat(50));
    
    Object.entries(results).forEach(([test, result]) => {
        const status = result ? '✅ 通过' : '❌ 失败';
        console.log(`${test}: ${status}`);
    });
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
        console.log('\n🎉 所有验证通过！修复成功！');
        console.log('💡 建议手动测试：点击左侧"多云管理"分类，检查右侧是否显示完整的三级结构');
    } else {
        console.log('\n⚠️  部分验证失败，请检查相关问题');
    }
    
    return allPassed;
}

// 如果在浏览器环境中，自动运行验证
if (typeof window !== 'undefined') {
    // 等待页面加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runVerification, 1000);
        });
    } else {
        setTimeout(runVerification, 1000);
    }
} else {
    // Node.js 环境
    console.log('请在浏览器控制台中运行此脚本');
}

// 导出验证函数供手动调用
if (typeof window !== 'undefined') {
    window.verifyThreeLevelNavFix = runVerification;
}
